# 智能重排与优化模块

## 模块概述

智能重排与优化模块负责对粗排检索结果进行精细化处理，通过深度语义重排、多样性优化和后处理过滤，确保最终返回给用户的结果具有高相关性、高质量和良好的多样性。该模块采用多阶段优化策略，平衡相关性与多样性的关系。

## 核心功能

### 1. 精排重排 (Fine-grained Ranking)
- **语义相关性重排**：使用更精细的语义模型重新评估相关性
- **上下文匹配分析**：分析查询与文档的上下文匹配程度
- **深度语义评分**：使用预训练的重排序模型进行精确评分
- **交互特征建模**：考虑查询-文档对的交互特征

### 2. 多样性优化 (Diversity Optimization)
- **结果去重**：识别和移除重复或高度相似的结果
- **多样性评估**：评估结果集的多样性程度
- **覆盖度优化**：确保结果覆盖查询的不同方面
- **信息增益最大化**：优化结果的信息增益

### 3. 后处理过滤 (Post-processing)
- **相关性阈值过滤**：过滤低相关性结果
- **质量评估过滤**：基于内容质量进行过滤
- **最终排序**：综合考虑多个因素的最终排序
- **结果封装**：为生成模块准备标准化数据

## 技术架构

### 1. 重排序服务接口
```java
public interface RerankingService {
    List<RankedResult> rerank(List<Candidate> candidates, SearchQuery query, RerankingConfig config);
    RerankingResult semanticRerank(List<Candidate> candidates, SearchQuery query, SemanticRerankConfig config);
    List<RankedResult> contextualRerank(List<Candidate> candidates, SearchQuery query, ContextualRerankConfig config);
    double calculateRelevanceScore(Candidate candidate, SearchQuery query, RelevanceConfig config);
}

@Data
@Builder
public class RankedResult {
    private Long documentId;
    private Long chunkId;
    private String content;
    private double originalScore;
    private double rerankScore;
    private double finalScore;
    private Map<String, Object> metadata;
    private List<String> highlights;
    private RelevanceExplanation explanation;
    private long processingTime;
}

@Data
@Builder
public class RelevanceExplanation {
    private double semanticScore;
    private double contextualScore;
    private double qualityScore;
    private List<String> matchedConcepts;
    private List<String> reasoningSteps;
    private double confidence;
}
```

### 2. 重排序服务实现
```java
@Component
public class RerankingServiceImpl implements RerankingService {

    @Autowired
    private SemanticRerankingModel semanticModel;

    @Autowired
    private ContextualAnalyzer contextualAnalyzer;

    @Autowired
    private QualityAssessor qualityAssessor;

    @Override
    public List<RankedResult> rerank(List<Candidate> candidates, SearchQuery query, RerankingConfig config) {
        List<RankedResult> rankedResults = new ArrayList<>();
        
        for (Candidate candidate : candidates) {
            long startTime = System.currentTimeMillis();
            
            // 语义重排评分
            double semanticScore = calculateSemanticScore(candidate, query, config);
            
            // 上下文匹配评分
            double contextualScore = calculateContextualScore(candidate, query, config);
            
            // 质量评分
            double qualityScore = calculateQualityScore(candidate, config);
            
            // 综合评分
            double finalScore = calculateFinalScore(semanticScore, contextualScore, qualityScore, config);
            
            // 生成解释
            RelevanceExplanation explanation = generateExplanation(
                candidate, query, semanticScore, contextualScore, qualityScore, config);
            
            RankedResult rankedResult = RankedResult.builder()
                .documentId(candidate.getDocumentId())
                .chunkId(candidate.getChunkId())
                .content(candidate.getContent())
                .originalScore(candidate.getScore())
                .rerankScore(finalScore)
                .finalScore(finalScore)
                .metadata(candidate.getMetadata())
                .highlights(generateHighlights(candidate, query))
                .explanation(explanation)
                .processingTime(System.currentTimeMillis() - startTime)
                .build();
            
            rankedResults.add(rankedResult);
        }
        
        // 排序
        rankedResults.sort((r1, r2) -> Double.compare(r2.getFinalScore(), r1.getFinalScore()));
        
        return rankedResults;
    }

    private double calculateSemanticScore(Candidate candidate, SearchQuery query, RerankingConfig config) {
        // 使用深度语义模型计算相关性
        String queryText = query.getText();
        String candidateText = candidate.getContent();
        
        // 构建查询-文档对
        QueryDocumentPair pair = QueryDocumentPair.builder()
            .query(queryText)
            .document(candidateText)
            .build();
        
        // 使用预训练的重排序模型
        return semanticModel.calculateRelevance(pair, config.getSemanticConfig());
    }

    private double calculateContextualScore(Candidate candidate, SearchQuery query, RerankingConfig config) {
        // 分析上下文匹配程度
        ContextualAnalysisResult result = contextualAnalyzer.analyze(
            candidate.getContent(), query.getText(), config.getContextualConfig());
        
        double score = 0.0;
        
        // 概念匹配得分
        score += result.getConceptMatchScore() * config.getConceptWeight();
        
        // 实体匹配得分
        score += result.getEntityMatchScore() * config.getEntityWeight();
        
        // 语义连贯性得分
        score += result.getCoherenceScore() * config.getCoherenceWeight();
        
        // 完整性得分
        score += result.getCompletenessScore() * config.getCompletenessWeight();
        
        return Math.min(score, 1.0);
    }

    private double calculateQualityScore(Candidate candidate, RerankingConfig config) {
        // 评估内容质量
        QualityAssessmentResult result = qualityAssessor.assess(
            candidate.getContent(), config.getQualityConfig());
        
        double score = 0.0;
        
        // 内容长度适中性
        score += result.getLengthScore() * config.getLengthWeight();
        
        // 语言流畅性
        score += result.getFluencyScore() * config.getFluencyWeight();
        
        // 信息密度
        score += result.getInformationDensityScore() * config.getInformationDensityWeight();
        
        // 结构完整性
        score += result.getStructureScore() * config.getStructureWeight();
        
        return Math.min(score, 1.0);
    }

    private double calculateFinalScore(double semanticScore, double contextualScore, 
                                     double qualityScore, RerankingConfig config) {
        return semanticScore * config.getSemanticWeight() +
               contextualScore * config.getContextualWeight() +
               qualityScore * config.getQualityWeight();
    }
}
```

### 3. 多样性优化器
```java
@Component
public class DiversityOptimizer {

    @Autowired
    private SimilarityCalculator similarityCalculator;

    @Autowired
    private TopicAnalyzer topicAnalyzer;

    public List<RankedResult> optimize(List<RankedResult> results, DiversityConfig config) {
        if (results.size() <= config.getMinResults()) {
            return results;
        }

        List<RankedResult> optimizedResults = new ArrayList<>();
        Set<String> selectedTopics = new HashSet<>();
        
        // 第一步：选择最高分的结果
        RankedResult topResult = results.get(0);
        optimizedResults.add(topResult);
        selectedTopics.addAll(extractTopics(topResult, config));
        
        // 第二步：基于多样性选择其余结果
        for (int i = 1; i < results.size() && optimizedResults.size() < config.getMaxResults(); i++) {
            RankedResult candidate = results.get(i);
            
            // 计算多样性分数
            double diversityScore = calculateDiversityScore(candidate, optimizedResults, selectedTopics, config);
            
            // 计算综合分数（相关性 + 多样性）
            double combinedScore = candidate.getFinalScore() * config.getRelevanceWeight() +
                                 diversityScore * config.getDiversityWeight();
            
            // 如果综合分数足够高，则选择该结果
            if (combinedScore > config.getSelectionThreshold()) {
                optimizedResults.add(candidate);
                selectedTopics.addAll(extractTopics(candidate, config));
            }
        }
        
        return optimizedResults;
    }

    private double calculateDiversityScore(RankedResult candidate, 
                                         List<RankedResult> selectedResults,
                                         Set<String> selectedTopics,
                                         DiversityConfig config) {
        double diversityScore = 1.0;
        
        // 计算与已选结果的相似度
        for (RankedResult selected : selectedResults) {
            double similarity = similarityCalculator.calculateSimilarity(
                candidate.getContent(), selected.getContent());
            diversityScore *= (1.0 - similarity);
        }
        
        // 计算主题多样性
        Set<String> candidateTopics = extractTopics(candidate, config);
        long newTopics = candidateTopics.stream()
            .filter(topic -> !selectedTopics.contains(topic))
            .count();
        
        double topicDiversityScore = (double) newTopics / candidateTopics.size();
        
        return diversityScore * config.getContentDiversityWeight() +
               topicDiversityScore * config.getTopicDiversityWeight();
    }

    private Set<String> extractTopics(RankedResult result, DiversityConfig config) {
        return topicAnalyzer.extractTopics(result.getContent(), config.getTopicConfig());
    }
}
```

### 4. 后处理过滤器
```java
@Component
public class PostProcessor {

    @Autowired
    private RelevanceThresholdFilter relevanceFilter;

    @Autowired
    private QualityFilter qualityFilter;

    @Autowired
    private DuplicationFilter duplicationFilter;

    public List<RankedResult> process(List<RankedResult> results, PostProcessingConfig config) {
        List<RankedResult> processedResults = new ArrayList<>(results);
        
        // 第一步：相关性阈值过滤
        if (config.isEnableRelevanceFilter()) {
            processedResults = relevanceFilter.filter(processedResults, config.getRelevanceThreshold());
        }
        
        // 第二步：质量过滤
        if (config.isEnableQualityFilter()) {
            processedResults = qualityFilter.filter(processedResults, config.getQualityThreshold());
        }
        
        // 第三步：去重过滤
        if (config.isEnableDeduplication()) {
            processedResults = duplicationFilter.filter(processedResults, config.getDuplicationThreshold());
        }
        
        // 第四步：最终排序
        processedResults = finalSort(processedResults, config);
        
        // 第五步：限制结果数量
        if (processedResults.size() > config.getMaxFinalResults()) {
            processedResults = processedResults.subList(0, config.getMaxFinalResults());
        }
        
        // 第六步：添加排序位置信息
        for (int i = 0; i < processedResults.size(); i++) {
            processedResults.get(i).getMetadata().put("rank", i + 1);
        }
        
        return processedResults;
    }

    private List<RankedResult> finalSort(List<RankedResult> results, PostProcessingConfig config) {
        return results.stream()
            .sorted((r1, r2) -> {
                // 主要按最终分数排序
                int scoreComparison = Double.compare(r2.getFinalScore(), r1.getFinalScore());
                if (scoreComparison != 0) {
                    return scoreComparison;
                }
                
                // 次要按原始分数排序
                int originalScoreComparison = Double.compare(r2.getOriginalScore(), r1.getOriginalScore());
                if (originalScoreComparison != 0) {
                    return originalScoreComparison;
                }
                
                // 最后按文档ID排序（保证稳定性）
                return Long.compare(r1.getDocumentId(), r2.getDocumentId());
            })
            .collect(Collectors.toList());
    }
}
```

## 流程图

```mermaid
graph TD
    A[接收粗排候选结果] --> B[精排重排阶段]
    
    B --> C[语义相关性重排]
    B --> D[上下文匹配分析]
    B --> E[深度语义评分]
    
    C --> F[多样性优化阶段]
    D --> F
    E --> F
    
    F --> G[结果去重]
    F --> H[多样性评估]
    F --> I[覆盖度优化]
    
    G --> J[后处理过滤阶段]
    H --> J
    I --> J
    
    J --> K[相关性阈值过滤]
    J --> L[质量评估过滤]
    J --> M[最终排序]
    
    K --> N[结果封装]
    L --> N
    M --> N
    
    N --> O[精排结果输出]
    O --> P[传递给自我反思模块]

    style A fill:#e1f5fe
    style O fill:#c8e6c9
    style P fill:#fff3e0
```

## 配置参数

### 重排序配置
```yaml
reranking:
  enabled: true
  semantic_weight: 0.5
  contextual_weight: 0.3
  quality_weight: 0.2
  
  semantic:
    model_name: "cross-encoder/ms-marco-MiniLM-L-6-v2"
    max_length: 512
    batch_size: 32
    
  contextual:
    concept_weight: 0.4
    entity_weight: 0.3
    coherence_weight: 0.2
    completeness_weight: 0.1
    
diversity:
  enabled: true
  max_results: 20
  relevance_weight: 0.7
  diversity_weight: 0.3
  content_diversity_weight: 0.6
  topic_diversity_weight: 0.4
  selection_threshold: 0.6
  
post_processing:
  relevance_threshold: 0.3
  quality_threshold: 0.5
  duplication_threshold: 0.8
  max_final_results: 10
```

## 性能指标

- **重排序准确率**：>90%
- **多样性提升率**：>25%
- **相关性保持率**：>95%
- **平均处理时间**：<200ms
- **用户满意度**：>8.5/10
