# 混合检索引擎模块

## 模块概述

混合检索引擎模块是检索系统的核心执行引擎，负责将重写后的查询转换为具体的检索操作。该模块集成了稠密向量检索、稀疏向量检索和Milvus原生混合检索功能，通过多种检索策略的融合，实现高精度、高召回的文档检索。

## 核心功能

### 1. Milvus混合检索
- **原生混合检索**：使用Milvus 2.4+的原生混合检索功能
- **多向量检索**：同时检索稠密向量和稀疏向量
- **RRF融合**：使用Reciprocal Rank Fusion算法融合结果
- **权重调节**：动态调整稠密向量和稀疏向量的权重

### 2. 稠密向量检索
- **语义相似度计算**：基于深度学习的语义向量匹配
- **高效索引**：使用HNSW或IVF索引加速检索
- **相似度度量**：支持余弦相似度、内积、欧氏距离等

### 3. 稀疏向量检索
- **关键词精确匹配**：保留传统关键词检索的精确性
- **稀疏向量生成**：使用SPLADE、BGE-M3等模型
- **TF-IDF权重**：结合传统信息检索权重

### 4. 检索结果融合
- **多策略融合**：融合不同检索策略的结果
- **分数归一化**：统一不同检索方法的评分标准
- **候选集合并**：智能合并和去重检索结果

## 技术架构

### 1. Milvus混合检索服务
```java
@Component
public class MilvusHybridSearchService {

    @Autowired
    private MilvusClient milvusClient;

    @Autowired
    private DenseVectorService denseVectorService;

    @Autowired
    private SparseVectorService sparseVectorService;

    public HybridSearchResult hybridSearch(SearchQuery query, HybridSearchConfig config) {
        // 生成稠密向量和稀疏向量
        float[] denseVector = denseVectorService.generateDenseVector(query.getText());
        SparseFloatVector sparseVector = sparseVectorService.generateSparseVector(query.getText());

        // 构建Milvus混合检索请求
        HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
            .collectionName(config.getCollectionName())
            .searchRequests(buildSearchRequests(denseVector, sparseVector, config))
            .ranker(RRFRanker.builder()
                .k(config.getRrfK())
                .build())
            .topK(config.getTopK())
            .build();

        // 执行混合检索
        SearchResp searchResp = milvusClient.hybridSearch(hybridSearchReq);

        return processHybridSearchResults(searchResp, query, config);
    }

    private List<SearchReq> buildSearchRequests(float[] denseVector,
                                              SparseFloatVector sparseVector,
                                              HybridSearchConfig config) {
        List<SearchReq> searchRequests = new ArrayList<>();

        // 稠密向量检索请求
        SearchReq denseSearchReq = SearchReq.builder()
            .collectionName(config.getCollectionName())
            .data(Collections.singletonList(denseVector))
            .annsField(config.getDenseVectorField())
            .topK(config.getDenseTopK())
            .metricType(MetricType.COSINE)
            .params(config.getDenseSearchParams())
            .build();
        searchRequests.add(denseSearchReq);

        // 稀疏向量检索请求
        SearchReq sparseSearchReq = SearchReq.builder()
            .collectionName(config.getCollectionName())
            .data(Collections.singletonList(sparseVector))
            .annsField(config.getSparseVectorField())
            .topK(config.getSparseTopK())
            .metricType(MetricType.IP)
            .params(config.getSparseSearchParams())
            .build();
        searchRequests.add(sparseSearchReq);

        return searchRequests;
    }

    private HybridSearchResult processHybridSearchResults(SearchResp searchResp,
                                                        SearchQuery query,
                                                        HybridSearchConfig config) {
        List<HybridCandidate> candidates = new ArrayList<>();

        for (SearchResultData resultData : searchResp.getResults()) {
            for (Object result : resultData.getResults()) {
                SearchResultItem item = (SearchResultItem) result;
                
                HybridCandidate candidate = HybridCandidate.builder()
                    .documentId(item.getLongField("document_id"))
                    .chunkId(item.getLongField("chunk_id"))
                    .content(item.getStringField("content"))
                    .score(item.getScore())
                    .denseScore(item.getFloatField("dense_score"))
                    .sparseScore(item.getFloatField("sparse_score"))
                    .metadata(parseMetadata(item.getStringField("metadata")))
                    .searchMethod(SearchMethod.HYBRID)
                    .build();
                
                candidates.add(candidate);
            }
        }

        return HybridSearchResult.builder()
            .candidates(candidates)
            .totalHits(searchResp.getResults().size())
            .searchTime(searchResp.getSearchTime())
            .query(query)
            .build();
    }
}
```

### 2. 向量检索服务
```java
@Component
public class VectorSearchService {
    
    @Autowired
    private MilvusClient milvusClient;
    
    @Autowired
    private EmbeddingService embeddingService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public List<VectorCandidate> search(SearchQuery query, VectorSearchConfig config) {
        // 查询缓存
        String cacheKey = generateCacheKey(query, config);
        List<VectorCandidate> cachedResults = getCachedResults(cacheKey);
        if (cachedResults != null && config.isEnableCaching()) {
            return cachedResults;
        }
        
        // 查询向量化
        float[] queryVector = embeddingService.generateEmbedding(
            query.getText(), config.getEmbeddingConfig());
        
        // 构建搜索参数
        SearchParam searchParam = buildSearchParam(query, queryVector, config);
        
        // 执行向量搜索
        SearchResult searchResult = milvusClient.search(searchParam);
        
        // 转换结果
        List<VectorCandidate> candidates = convertSearchResult(searchResult, query);
        
        // 缓存结果
        if (config.isEnableCaching()) {
            cacheResults(cacheKey, candidates, config.getCacheExpiration());
        }
        
        return candidates;
    }
    
    private SearchParam buildSearchParam(SearchQuery query, float[] queryVector, 
                                       VectorSearchConfig config) {
        return SearchParam.builder()
            .collectionName(getCollectionName(query.getKnowledgeBaseId()))
            .vectors(Collections.singletonList(queryVector))
            .topK(config.getTopK())
            .metricType(config.getMetricType())
            .searchParams(buildMilvusSearchParams(config))
            .expr(buildFilterExpression(query, config))
            .outputFields(Arrays.asList("content", "metadata", "chunk_id", "document_id"))
            .build();
    }
    
    private String buildFilterExpression(SearchQuery query, VectorSearchConfig config) {
        List<String> conditions = new ArrayList<>();
        
        // 知识库过滤
        conditions.add("knowledge_base_id == " + query.getKnowledgeBaseId());
        
        // 时间范围过滤
        if (query.getTimeRange() != null) {
            conditions.add("created_time >= " + query.getTimeRange().getStart());
            conditions.add("created_time <= " + query.getTimeRange().getEnd());
        }
        
        // 文档类型过滤
        if (query.getDocumentTypes() != null && !query.getDocumentTypes().isEmpty()) {
            String typeFilter = query.getDocumentTypes().stream()
                .map(type -> "\"" + type + "\"")
                .collect(Collectors.joining(", "));
            conditions.add("document_type in [" + typeFilter + "]");
        }
        
        // 自定义过滤条件
        if (config.getCustomFilters() != null) {
            conditions.addAll(config.getCustomFilters());
        }
        
        return String.join(" && ", conditions);
    }
    
    private List<VectorCandidate> convertSearchResult(SearchResult searchResult, SearchQuery query) {
        List<VectorCandidate> candidates = new ArrayList<>();
        
        for (SearchResultItem item : searchResult.getItems()) {
            VectorCandidate candidate = VectorCandidate.builder()
                .chunkId(item.getLongField("chunk_id"))
                .documentId(item.getLongField("document_id"))
                .content(item.getStringField("content"))
                .score(item.getScore())
                .distance(item.getDistance())
                .metadata(parseMetadata(item.getStringField("metadata")))
                .searchMethod(SearchMethod.VECTOR)
                .build();
            
            candidates.add(candidate);
        }
        
        return candidates;
    }
}
```

### 3. 关键词检索服务
```java
@Component
public class KeywordSearchService {
    
    @Autowired
    private ElasticsearchClient elasticsearchClient;
    
    @Autowired
    private QueryExpansionService queryExpansionService;
    
    public List<KeywordCandidate> search(SearchQuery query, KeywordSearchConfig config) {
        // 查询扩展
        ExpandedQuery expandedQuery = queryExpansionService.expand(query, config);
        
        // 构建ES查询
        SearchRequest searchRequest = buildElasticsearchQuery(expandedQuery, config);
        
        // 执行搜索
        SearchResponse<DocumentChunk> response = elasticsearchClient.search(
            searchRequest, DocumentChunk.class);
        
        // 转换结果
        return convertElasticsearchResult(response, query);
    }
    
    private SearchRequest buildElasticsearchQuery(ExpandedQuery expandedQuery, 
                                                KeywordSearchConfig config) {
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();
        
        // 主查询
        boolQuery.must(buildMainQuery(expandedQuery, config));
        
        // 过滤条件
        boolQuery.filter(buildFilters(expandedQuery, config));
        
        // 构建搜索请求
        return SearchRequest.of(s -> s
            .index(getIndexName(expandedQuery.getKnowledgeBaseId()))
            .query(boolQuery.build()._toQuery())
            .size(config.getTopK())
            .highlight(buildHighlight(config))
            .sort(buildSort(config))
        );
    }
    
    private Query buildMainQuery(ExpandedQuery expandedQuery, KeywordSearchConfig config) {
        MultiMatchQuery.Builder multiMatch = new MultiMatchQuery.Builder()
            .query(expandedQuery.getOriginalText())
            .fields(Arrays.asList("content^2.0", "title^3.0", "summary^1.5"))
            .type(TextQueryType.BestFields)
            .fuzziness("AUTO");
        
        // 添加同义词查询
        if (!expandedQuery.getSynonyms().isEmpty()) {
            BoolQuery.Builder synonymQuery = new BoolQuery.Builder();
            for (String synonym : expandedQuery.getSynonyms()) {
                synonymQuery.should(MatchQuery.of(m -> m
                    .field("content")
                    .query(synonym)
                    .boost(0.8f)
                )._toQuery());
            }
            
            return BoolQuery.of(b -> b
                .must(multiMatch.build()._toQuery())
                .should(synonymQuery.build()._toQuery())
            )._toQuery();
        }
        
        return multiMatch.build()._toQuery();
    }
    
    private List<KeywordCandidate> convertElasticsearchResult(SearchResponse<DocumentChunk> response, 
                                                            SearchQuery query) {
        List<KeywordCandidate> candidates = new ArrayList<>();
        
        for (Hit<DocumentChunk> hit : response.hits().hits()) {
            DocumentChunk chunk = hit.source();
            
            KeywordCandidate candidate = KeywordCandidate.builder()
                .chunkId(chunk.getId())
                .documentId(chunk.getDocumentId())
                .content(chunk.getContent())
                .score(hit.score())
                .highlights(extractHighlights(hit.highlight()))
                .searchMethod(SearchMethod.KEYWORD)
                .bm25Score(calculateBM25Score(hit, query))
                .build();
            
            candidates.add(candidate);
        }
        
        return candidates;
    }
}
```

### 4. 检索结果融合器
```java
@Component
public class SearchResultFuser {
    
    public List<Candidate> fuseResults(List<VectorCandidate> vectorResults,
                                     List<KeywordCandidate> keywordResults,
                                     List<HybridCandidate> hybridResults,
                                     FusionConfig config) {
        Map<String, Candidate> fusedResults = new HashMap<>();
        
        // 处理向量检索结果
        for (VectorCandidate vectorCandidate : vectorResults) {
            String key = generateCandidateKey(vectorCandidate);
            Candidate candidate = convertToCandidate(vectorCandidate);
            candidate.setVectorScore(vectorCandidate.getScore());
            fusedResults.put(key, candidate);
        }
        
        // 处理关键词检索结果
        for (KeywordCandidate keywordCandidate : keywordResults) {
            String key = generateCandidateKey(keywordCandidate);
            if (fusedResults.containsKey(key)) {
                // 融合分数
                Candidate existing = fusedResults.get(key);
                existing.setKeywordScore(keywordCandidate.getScore());
                existing.setFusedScore(calculateFusedScore(existing, config));
            } else {
                Candidate candidate = convertToCandidate(keywordCandidate);
                candidate.setKeywordScore(keywordCandidate.getScore());
                fusedResults.put(key, candidate);
            }
        }
        
        // 处理混合检索结果
        for (HybridCandidate hybridCandidate : hybridResults) {
            String key = generateCandidateKey(hybridCandidate);
            if (fusedResults.containsKey(key)) {
                // 更新混合分数
                Candidate existing = fusedResults.get(key);
                existing.setHybridScore(hybridCandidate.getScore());
                existing.setFusedScore(calculateFusedScore(existing, config));
            } else {
                Candidate candidate = convertToCandidate(hybridCandidate);
                candidate.setHybridScore(hybridCandidate.getScore());
                fusedResults.put(key, candidate);
            }
        }
        
        // 排序并返回
        return fusedResults.values().stream()
            .sorted((c1, c2) -> Double.compare(c2.getFusedScore(), c1.getFusedScore()))
            .limit(config.getMaxResults())
            .collect(Collectors.toList());
    }
    
    private double calculateFusedScore(Candidate candidate, FusionConfig config) {
        double vectorWeight = config.getVectorWeight();
        double keywordWeight = config.getKeywordWeight();
        double hybridWeight = config.getHybridWeight();
        
        double totalScore = 0.0;
        double totalWeight = 0.0;
        
        if (candidate.getVectorScore() != null) {
            totalScore += candidate.getVectorScore() * vectorWeight;
            totalWeight += vectorWeight;
        }
        
        if (candidate.getKeywordScore() != null) {
            totalScore += candidate.getKeywordScore() * keywordWeight;
            totalWeight += keywordWeight;
        }
        
        if (candidate.getHybridScore() != null) {
            totalScore += candidate.getHybridScore() * hybridWeight;
            totalWeight += hybridWeight;
        }
        
        return totalWeight > 0 ? totalScore / totalWeight : 0.0;
    }
}
```

## 流程图

```mermaid
graph TD
    A[接收重写查询列表] --> B[并行检索执行]
    
    B --> C[Milvus混合检索]
    B --> D[稠密向量检索]
    B --> E[稀疏向量检索]
    B --> F[关键词检索]
    
    C --> G[RRF融合]
    D --> H[语义相似度计算]
    E --> I[关键词匹配]
    F --> J[BM25评分]
    
    G --> K[结果归一化]
    H --> K
    I --> K
    J --> K
    
    K --> L[多策略融合]
    L --> M[候选集合并]
    M --> N[分数重新计算]
    N --> O[粗排结果输出]
    O --> P[传递给智能重排模块]

    style A fill:#e1f5fe
    style O fill:#c8e6c9
    style P fill:#fff3e0
```

## 配置参数

### 混合检索配置
```yaml
hybrid_search:
  enabled: true
  collection_name: "knowledge_base"
  top_k: 100
  rrf_k: 60
  
  dense_vector:
    field: "dense_vector"
    top_k: 50
    metric_type: "COSINE"
    search_params:
      nprobe: 16
      
  sparse_vector:
    field: "sparse_vector"
    top_k: 50
    metric_type: "IP"
    search_params:
      drop_ratio_search: 0.2
      
fusion:
  vector_weight: 0.4
  keyword_weight: 0.3
  hybrid_weight: 0.3
  max_results: 100
```

## 性能指标

- **检索召回率**：>85%
- **检索精确率**：>80%
- **平均检索时间**：<500ms
- **并发处理能力**：>1000 QPS
- **系统可用性**：>99.9%
