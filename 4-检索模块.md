# 检索模块 (Retrieval)

## 方案说明

### 1. 技术方案概述
检索模块是RAG系统的核心组件，负责根据用户查询从向量数据库中检索最相关的文档片段，采用多阶段检索和混合检索策略，确保检索结果的准确性和相关性。该模块融合了向量检索、关键词检索、语义重排等多种技术，实现高质量的信息检索。

### 2. 核心技术特点
- **CoT推理链**：逐步推理的查询理解和检索决策过程
- **自我反思机制**：检索结果的自动评估和策略优化
- **智能查询重写**：多策略查询扩展、改写和优化技术
- **多阶段检索**：粗排、精排、多样性优化、后处理的四阶段检索流程
- **混合检索策略**：基于Milvus的稠密向量+稀疏向量混合检索
- **智能重排序**：基于深度学习的语义重排序算法
- **多样性优化**：确保检索结果的多样性和覆盖面
- **实时优化**：基于用户反馈的检索效果持续优化

### 3. 技术架构优势
- **高精度**：多阶段精细化检索，提高检索准确率
- **高效率**：分层检索架构，平衡检索质量和性能
- **可扩展**：支持多种检索算法和评分策略
- **智能化**：自适应检索参数和策略选择

## 流程图

```mermaid
graph TD
    A[用户查询输入] --> B[查询预处理]
    B --> C[CoT推理链启动]
    C --> C1[查询意图分析]
    C --> C2[关键概念识别]
    C --> C3[检索策略推理]
    C --> C4[推理路径记录]

    C1 --> D[查询理解分析]
    C2 --> D
    C3 --> D
    C4 --> D

    D --> E[查询重写模块]
    E --> E1[查询扩展]
    E --> E2[查询改写]
    E --> E3[查询分解]
    E --> E4[多查询生成]

    E1 --> F[第一阶段: 粗排检索]
    E2 --> F
    E3 --> F
    E4 --> F

    E --> F[稠密向量检索]
    E --> G[稀疏向量检索]
    E --> H[Milvus混合检索]

    F --> I[语义相似度计算]
    I --> J[稠密向量TopK召回]

    G --> K[稀疏向量匹配]
    K --> L[关键词精确匹配评分]

    H --> M[Milvus混合检索结果]
    M --> N[初步候选集]

    J --> N
    L --> N

    N --> O[第二阶段: 精排重排]
    O --> P[语义相关性重排]
    P --> Q[上下文匹配分析]
    Q --> R[深度语义评分]

    R --> S[第三阶段: 多样性优化]
    S --> T[结果去重]
    T --> U[多样性评估]
    U --> V[覆盖度优化]

    V --> W[第四阶段: 后处理过滤]
    W --> X[相关性阈值过滤]
    X --> Y[最终排序]

    Y --> Z[自我反思机制]
    Z --> Z1[检索质量评估]
    Z --> Z2[策略有效性分析]
    Z --> Z3[改进点识别]
    Z --> Z4[参数动态调整]

    Z1 --> AA{反思结果判断}
    Z2 --> AA
    Z3 --> AA
    Z4 --> AA

    AA -->|需要重新检索| BB[策略调整重检索]
    AA -->|结果满意| CC[检索结果输出]

    BB --> F
    CC --> DD[生成模块]

    style A fill:#e1f5fe
    style CC fill:#c8e6c9
    style DD fill:#fff3e0
```

## 流程步骤说明

### 阶段零：CoT推理链与查询理解
1. **查询预处理**：
   - 查询清洗：移除无意义字符和停用词
   - 查询标准化：统一大小写、标点符号处理
   - 拼写纠错：自动纠正拼写错误
   - 意图识别：识别查询的搜索意图和类型

2. **CoT推理链启动**：
   - **查询意图分析**：
     - 步骤1：分析查询的表面语义
     - 步骤2：推理用户的真实意图
     - 步骤3：识别查询的类型（事实性、分析性、创造性等）
     - 步骤4：评估查询的复杂度和专业程度

   - **关键概念识别**：
     - 步骤1：提取查询中的核心概念和实体
     - 步骤2：分析概念间的层次关系
     - 步骤3：识别隐含的概念和背景知识
     - 步骤4：构建概念图谱和关联网络

   - **检索策略推理**：
     - 步骤1：基于查询特征推理最优检索策略
     - 步骤2：分析不同策略的适用性和效果
     - 步骤3：预测各策略的检索结果质量
     - 步骤4：制定多策略组合方案

   - **推理路径记录**：
     - 记录每个推理步骤的输入、过程和输出
     - 保存中间结果和决策依据
     - 构建推理链的可追溯路径
     - 为后续反思提供基础数据

3. **查询理解分析**：
   - **语义分析**：基于CoT推理结果的深度语义理解
   - **实体识别**：结合推理链识别的关键实体
   - **关系抽取**：分析实体间的复杂关系
   - **难度评估**：基于推理复杂度评估查询难度
   - **上下文分析**：综合推理链分析查询上下文

3. **查询重写模块**：
   - **查询扩展（Query Expansion）**：
     - 同义词扩展：添加查询词的同义词和近义词
     - 相关词扩展：基于语义相关性添加相关词汇
     - 概念扩展：添加上位概念和下位概念
     - 历史查询扩展：基于用户历史查询进行扩展

   - **查询改写（Query Reformulation）**：
     - 语法改写：调整查询的语法结构
     - 语义改写：保持语义的前提下改写表达方式
     - 简化改写：将复杂查询简化为多个简单查询
     - 专业术语转换：将口语化表达转换为专业术语

   - **查询分解（Query Decomposition）**：
     - 复合查询分解：将复合查询分解为多个子查询
     - 逻辑分解：按照逻辑关系分解查询
     - 时间分解：按照时间维度分解查询
     - 主题分解：按照主题维度分解查询

   - **多查询生成（Multi-Query Generation）**：
     - 并行查询生成：生成多个并行的查询变体
     - 递进查询生成：生成从宽泛到具体的查询序列
     - 角度查询生成：从不同角度生成查询
     - 粒度查询生成：生成不同粒度的查询

4. **检索策略选择**：
   - 根据重写后的查询特征选择最适合的检索策略
   - 动态调整检索参数和权重
   - 确定检索的深度和广度
   - 选择最优的查询组合策略

### 阶段一：粗排检索（Coarse Retrieval）
#### 稠密向量检索分支
1. **查询向量化**：
   - 使用与文档相同的稠密向量模型对查询进行向量化
   - 确保查询向量与文档向量在同一语义空间
   - 处理查询向量的标准化和归一化

2. **语义相似度计算**：
   - **余弦相似度**：计算查询向量与文档向量的余弦相似度
   - **内积相似度**：计算向量的内积相似度
   - **欧氏距离**：计算向量间的欧氏距离
   - 在Milvus中使用高效的HNSW或IVF索引

3. **稠密向量TopK召回**：
   - 使用Milvus的高效向量检索算法
   - 召回Top-K个语义最相似的文档片段
   - 利用Milvus的分布式检索能力

#### 稀疏向量检索分支
1. **稀疏向量生成**：
   - 使用SPLADE、BGE-M3等模型生成稀疏向量
   - 保留关键词的精确匹配能力
   - 结合TF-IDF权重优化稀疏向量

2. **稀疏向量匹配**：
   - 在Milvus中使用稀疏向量索引
   - 计算稀疏向量的点积相似度
   - 支持精确的关键词匹配

3. **关键词精确匹配评分**：
   - 精确匹配评分
   - 词频权重评分
   - 位置权重评分
   - 稀疏向量维度重要性评分

#### Milvus混合检索融合
1. **Milvus混合检索**：
   - **原生混合检索**：使用Milvus 2.4+的原生混合检索功能
   - **多向量检索**：同时检索稠密向量和稀疏向量
   - **RRF融合**：使用Reciprocal Rank Fusion算法融合结果
   - **权重调节**：动态调整稠密向量和稀疏向量的权重

### 阶段二：精排重排（Fine-grained Ranking）
1. **语义相关性重排**：
   - 使用更精细的语义模型重新评估相关性
   - 考虑查询与文档的深层语义匹配
   - 分析语义相似度的置信度

2. **上下文匹配分析**：
   - 分析查询与文档的上下文匹配程度
   - 考虑文档的上下文信息和背景知识
   - 评估答案的完整性和准确性

3. **深度语义评分**：
   - 使用预训练的重排序模型
   - 考虑查询-文档对的交互特征
   - 生成更准确的相关性分数

### 阶段三：多样性优化（Diversity Optimization）
1. **结果去重**：
   - 识别和移除重复或高度相似的结果
   - 使用语义相似度进行智能去重
   - 保留最高质量的代表性结果

2. **多样性评估**：
   - 评估结果集的多样性程度
   - 分析结果的主题分布和覆盖面
   - 计算多样性指标和分数

3. **覆盖度优化**：
   - 确保结果覆盖查询的不同方面
   - 平衡相关性和多样性
   - 优化结果的信息增益

### 阶段四：后处理过滤（Post-processing）
1. **相关性阈值过滤**：
   - 设置相关性阈值，过滤不相关结果
   - 动态调整阈值以适应不同查询
   - 确保返回结果的相关性标准

2. **最终排序**：
   - 综合考虑相关性、多样性等因素
   - 生成最终的排序结果
   - 为每个结果添加置信度分数

3. **结果封装**：
   - 封装检索结果为标准格式
   - 添加元数据和解释信息
   - 准备传递给生成模块的数据

### 阶段五：自我反思机制（Self-Reflection）
1. **检索质量评估**：
   - **相关性评估**：
     - 分析检索结果与原始查询的相关性
     - 评估结果的语义匹配程度
     - 检查是否遗漏重要信息
     - 计算相关性分布和一致性

   - **完整性评估**：
     - 评估检索结果是否完整回答查询
     - 分析信息覆盖的广度和深度
     - 识别信息缺口和不足之处
     - 评估结果的信息密度

   - **多样性评估**：
     - 分析结果的多样性和代表性
     - 检查是否存在信息偏向
     - 评估观点和角度的平衡性
     - 计算结果的信息熵

2. **策略有效性分析**：
   - **CoT推理效果评估**：
     - 分析推理链的逻辑性和合理性
     - 评估推理步骤的准确性
     - 检查推理结论与实际结果的一致性
     - 识别推理过程中的偏差和错误

   - **查询重写效果评估**：
     - 分析各种重写策略的贡献度
     - 评估重写查询的检索效果
     - 比较原始查询与重写查询的结果质量
     - 识别最有效的重写策略

   - **混合检索效果评估**：
     - 分析稠密向量和稀疏向量的贡献
     - 评估融合策略的有效性
     - 检查检索参数的合理性
     - 识别检索瓶颈和优化空间

3. **改进点识别**：
   - **策略改进建议**：
     - 基于效果评估提出策略调整建议
     - 识别需要优化的检索参数
     - 建议新的查询重写策略
     - 提出检索流程改进方案

   - **参数优化建议**：
     - 分析当前参数设置的合理性
     - 建议检索阈值的调整
     - 优化权重分配策略
     - 调整多样性控制参数

4. **参数动态调整**：
   - **实时参数调整**：
     - 基于反思结果动态调整检索参数
     - 优化查询重写策略权重
     - 调整混合检索的融合比例
     - 更新相关性阈值设置

   - **策略切换决策**：
     - 判断是否需要重新执行检索
     - 决定是否切换检索策略
     - 评估重新检索的成本效益
     - 制定策略调整的执行计划

## 模块概述

检索模块是RAG系统的核心组件，负责根据用户查询从向量数据库中检索最相关的文档片段，采用多阶段检索和混合检索策略，确保检索结果的准确性和相关性。

## 核心功能架构

### 1. CoT推理服务架构

#### 1.1 CoT推理服务接口
```java
public interface CoTReasoningService {
    ReasoningChain analyzeQuery(SearchQuery query, CoTConfig config);
    IntentAnalysisResult analyzeIntent(String query, IntentAnalysisConfig config);
    ConceptExtractionResult extractConcepts(String query, ConceptExtractionConfig config);
    StrategyReasoningResult reasonStrategy(QueryContext context, StrategyReasoningConfig config);
    ReasoningPath recordReasoningPath(List<ReasoningStep> steps);
}

@Data
@Builder
public class ReasoningChain {
    private String queryId;
    private String originalQuery;
    private IntentAnalysisResult intentAnalysis;
    private ConceptExtractionResult conceptExtraction;
    private StrategyReasoningResult strategyReasoning;
    private ReasoningPath reasoningPath;
    private long totalReasoningTime;
    private double confidenceScore;
}

@Data
@Builder
public class ReasoningStep {
    private int stepNumber;
    private String stepType;
    private String description;
    private Map<String, Object> input;
    private Map<String, Object> output;
    private String reasoning;
    private double confidence;
    private long processingTime;
}
```

#### 1.2 CoT推理服务实现
```java
@Component
public class CoTReasoningServiceImpl implements CoTReasoningService {

    @Autowired
    private LLMService llmService;

    @Autowired
    private NLPService nlpService;

    @Autowired
    private KnowledgeGraphService knowledgeGraphService;

    @Override
    public ReasoningChain analyzeQuery(SearchQuery query, CoTConfig config) {
        String queryId = generateQueryId();
        long startTime = System.currentTimeMillis();

        List<ReasoningStep> reasoningSteps = new ArrayList<>();

        // 步骤1：查询意图分析
        IntentAnalysisResult intentAnalysis = analyzeIntent(query.getText(), config.getIntentAnalysisConfig());
        reasoningSteps.add(createReasoningStep(1, "INTENT_ANALYSIS",
            "分析查询的真实意图和类型",
            Map.of("query", query.getText()),
            Map.of("intent", intentAnalysis.getIntent(), "confidence", intentAnalysis.getConfidence()),
            intentAnalysis.getReasoning(),
            intentAnalysis.getConfidence()));

        // 步骤2：关键概念识别
        ConceptExtractionResult conceptExtraction = extractConcepts(query.getText(), config.getConceptExtractionConfig());
        reasoningSteps.add(createReasoningStep(2, "CONCEPT_EXTRACTION",
            "识别查询中的关键概念和实体",
            Map.of("query", query.getText(), "intent", intentAnalysis.getIntent()),
            Map.of("concepts", conceptExtraction.getConcepts(), "entities", conceptExtraction.getEntities()),
            conceptExtraction.getReasoning(),
            conceptExtraction.getConfidence()));

        // 步骤3：检索策略推理
        QueryContext context = QueryContext.builder()
            .query(query.getText())
            .intent(intentAnalysis.getIntent())
            .concepts(conceptExtraction.getConcepts())
            .entities(conceptExtraction.getEntities())
            .build();

        StrategyReasoningResult strategyReasoning = reasonStrategy(context, config.getStrategyReasoningConfig());
        reasoningSteps.add(createReasoningStep(3, "STRATEGY_REASONING",
            "基于查询特征推理最优检索策略",
            Map.of("context", context),
            Map.of("strategies", strategyReasoning.getRecommendedStrategies(),
                   "parameters", strategyReasoning.getOptimalParameters()),
            strategyReasoning.getReasoning(),
            strategyReasoning.getConfidence()));

        // 构建推理路径
        ReasoningPath reasoningPath = recordReasoningPath(reasoningSteps);

        // 计算整体置信度
        double overallConfidence = calculateOverallConfidence(reasoningSteps);

        return ReasoningChain.builder()
            .queryId(queryId)
            .originalQuery(query.getText())
            .intentAnalysis(intentAnalysis)
            .conceptExtraction(conceptExtraction)
            .strategyReasoning(strategyReasoning)
            .reasoningPath(reasoningPath)
            .totalReasoningTime(System.currentTimeMillis() - startTime)
            .confidenceScore(overallConfidence)
            .build();
    }

    @Override
    public IntentAnalysisResult analyzeIntent(String query, IntentAnalysisConfig config) {
        // 构建意图分析的CoT提示词
        String cotPrompt = buildIntentAnalysisPrompt(query, config);

        // 使用LLM进行逐步推理
        String llmResponse = llmService.generateText(cotPrompt, config.getLlmConfig());

        // 解析推理结果
        return parseIntentAnalysisResponse(llmResponse, query);
    }

    @Override
    public ConceptExtractionResult extractConcepts(String query, ConceptExtractionConfig config) {
        // 构建概念提取的CoT提示词
        String cotPrompt = buildConceptExtractionPrompt(query, config);

        // 使用LLM进行逐步推理
        String llmResponse = llmService.generateText(cotPrompt, config.getLlmConfig());

        // 结合NLP工具进行实体识别
        List<Entity> nlpEntities = nlpService.extractEntities(query);

        // 结合知识图谱进行概念扩展
        List<Concept> kgConcepts = knowledgeGraphService.findRelatedConcepts(query);

        // 融合多种方法的结果
        return fuseConceptExtractionResults(llmResponse, nlpEntities, kgConcepts, query);
    }

    @Override
    public StrategyReasoningResult reasonStrategy(QueryContext context, StrategyReasoningConfig config) {
        // 构建策略推理的CoT提示词
        String cotPrompt = buildStrategyReasoningPrompt(context, config);

        // 使用LLM进行策略推理
        String llmResponse = llmService.generateText(cotPrompt, config.getLlmConfig());

        // 解析策略推理结果
        return parseStrategyReasoningResponse(llmResponse, context);
    }

    private String buildIntentAnalysisPrompt(String query, IntentAnalysisConfig config) {
        return String.format(
            "请对以下查询进行逐步的意图分析：\n\n" +
            "查询：%s\n\n" +
            "请按照以下步骤进行分析：\n" +
            "1. 分析查询的表面语义和字面意思\n" +
            "2. 推理用户的真实意图和目的\n" +
            "3. 判断查询的类型（事实性、分析性、比较性、创造性等）\n" +
            "4. 评估查询的复杂度和专业程度\n" +
            "5. 总结分析结果和置信度\n\n" +
            "请为每个步骤提供详细的推理过程和依据。",
            query
        );
    }

    private String buildConceptExtractionPrompt(String query, ConceptExtractionConfig config) {
        return String.format(
            "请对以下查询进行逐步的概念分析：\n\n" +
            "查询：%s\n\n" +
            "请按照以下步骤进行分析：\n" +
            "1. 识别查询中的核心概念和关键词\n" +
            "2. 提取命名实体（人名、地名、机构名、时间等）\n" +
            "3. 分析概念间的层次关系和语义关联\n" +
            "4. 识别隐含的概念和背景知识\n" +
            "5. 构建概念图谱和关联网络\n\n" +
            "请为每个步骤提供详细的分析过程和结果。",
            query
        );
    }

    private String buildStrategyReasoningPrompt(QueryContext context, StrategyReasoningConfig config) {
        return String.format(
            "请基于以下查询上下文进行检索策略推理：\n\n" +
            "查询：%s\n" +
            "意图：%s\n" +
            "关键概念：%s\n\n" +
            "请按照以下步骤进行推理：\n" +
            "1. 分析查询特征和检索需求\n" +
            "2. 评估不同检索策略的适用性\n" +
            "3. 推理最优的检索策略组合\n" +
            "4. 确定关键参数的最优设置\n" +
            "5. 预测检索效果和潜在问题\n\n" +
            "可选的检索策略包括：向量检索、关键词检索、混合检索、查询扩展、查询改写等。",
            context.getQuery(),
            context.getIntent(),
            context.getConcepts()
        );
    }
}
```

### 2. 自我反思服务架构

#### 2.1 自我反思服务接口
```java
public interface SelfReflectionService {
    ReflectionResult reflect(RetrievalResult retrievalResult, ReasoningChain reasoningChain, ReflectionConfig config);
    QualityAssessment assessQuality(List<RankedResult> results, SearchQuery query, QualityAssessmentConfig config);
    StrategyEffectivenessAnalysis analyzeStrategyEffectiveness(RetrievalResult result, ReasoningChain reasoning);
    ImprovementSuggestions identifyImprovements(ReflectionResult reflection, ImprovementConfig config);
    ParameterAdjustment adjustParameters(ImprovementSuggestions suggestions, ParameterAdjustmentConfig config);
}

@Data
@Builder
public class ReflectionResult {
    private String reflectionId;
    private QualityAssessment qualityAssessment;
    private StrategyEffectivenessAnalysis strategyAnalysis;
    private ImprovementSuggestions improvements;
    private ParameterAdjustment parameterAdjustment;
    private boolean needsReretrieval;
    private double overallSatisfaction;
    private long reflectionTime;
}
```

#### 2.2 自我反思服务实现
```java
@Component
public class SelfReflectionServiceImpl implements SelfReflectionService {

    @Autowired
    private LLMService llmService;

    @Autowired
    private MetricsCalculator metricsCalculator;

    @Autowired
    private ParameterOptimizer parameterOptimizer;

    @Override
    public ReflectionResult reflect(RetrievalResult retrievalResult,
                                  ReasoningChain reasoningChain,
                                  ReflectionConfig config) {
        long startTime = System.currentTimeMillis();
        String reflectionId = generateReflectionId();

        // 1. 检索质量评估
        QualityAssessment qualityAssessment = assessQuality(
            retrievalResult.getResults(),
            retrievalResult.getQuery(),
            config.getQualityAssessmentConfig()
        );

        // 2. 策略有效性分析
        StrategyEffectivenessAnalysis strategyAnalysis = analyzeStrategyEffectiveness(
            retrievalResult, reasoningChain
        );

        // 3. 改进点识别
        ImprovementSuggestions improvements = identifyImprovements(
            ReflectionResult.builder()
                .qualityAssessment(qualityAssessment)
                .strategyAnalysis(strategyAnalysis)
                .build(),
            config.getImprovementConfig()
        );

        // 4. 参数动态调整
        ParameterAdjustment parameterAdjustment = adjustParameters(
            improvements, config.getParameterAdjustmentConfig()
        );

        // 5. 判断是否需要重新检索
        boolean needsReretrieval = shouldRetrieve(qualityAssessment, improvements, config);

        // 6. 计算整体满意度
        double overallSatisfaction = calculateOverallSatisfaction(
            qualityAssessment, strategyAnalysis, improvements
        );

        return ReflectionResult.builder()
            .reflectionId(reflectionId)
            .qualityAssessment(qualityAssessment)
            .strategyAnalysis(strategyAnalysis)
            .improvements(improvements)
            .parameterAdjustment(parameterAdjustment)
            .needsReretrieval(needsReretrieval)
            .overallSatisfaction(overallSatisfaction)
            .reflectionTime(System.currentTimeMillis() - startTime)
            .build();
    }

    @Override
    public QualityAssessment assessQuality(List<RankedResult> results,
                                         SearchQuery query,
                                         QualityAssessmentConfig config) {
        // 相关性评估
        RelevanceAssessment relevanceAssessment = assessRelevance(results, query, config);

        // 完整性评估
        CompletenessAssessment completenessAssessment = assessCompleteness(results, query, config);

        // 多样性评估
        DiversityAssessment diversityAssessment = assessDiversity(results, config);

        // 使用LLM进行综合质量评估
        String qualityPrompt = buildQualityAssessmentPrompt(results, query, config);
        String llmAssessment = llmService.generateText(qualityPrompt, config.getLlmConfig());

        LLMQualityAssessment llmQuality = parseLLMQualityAssessment(llmAssessment);

        return QualityAssessment.builder()
            .relevanceAssessment(relevanceAssessment)
            .completenessAssessment(completenessAssessment)
            .diversityAssessment(diversityAssessment)
            .llmQualityAssessment(llmQuality)
            .overallQualityScore(calculateOverallQualityScore(
                relevanceAssessment, completenessAssessment, diversityAssessment, llmQuality))
            .build();
    }

    @Override
    public StrategyEffectivenessAnalysis analyzeStrategyEffectiveness(RetrievalResult result,
                                                                    ReasoningChain reasoning) {
        // CoT推理效果评估
        CoTEffectivenessAnalysis cotAnalysis = analyzeCoTEffectiveness(reasoning, result);

        // 查询重写效果评估
        QueryRewriteEffectivenessAnalysis rewriteAnalysis = analyzeQueryRewriteEffectiveness(result);

        // 混合检索效果评估
        HybridRetrievalEffectivenessAnalysis hybridAnalysis = analyzeHybridRetrievalEffectiveness(result);

        // 整体策略效果评估
        double overallEffectiveness = calculateOverallEffectiveness(
            cotAnalysis, rewriteAnalysis, hybridAnalysis
        );

        return StrategyEffectivenessAnalysis.builder()
            .cotAnalysis(cotAnalysis)
            .rewriteAnalysis(rewriteAnalysis)
            .hybridAnalysis(hybridAnalysis)
            .overallEffectiveness(overallEffectiveness)
            .build();
    }

    @Override
    public ImprovementSuggestions identifyImprovements(ReflectionResult reflection,
                                                     ImprovementConfig config) {
        List<StrategySuggestion> strategySuggestions = new ArrayList<>();
        List<ParameterSuggestion> parameterSuggestions = new ArrayList<>();

        // 基于质量评估生成改进建议
        if (reflection.getQualityAssessment().getOverallQualityScore() < config.getQualityThreshold()) {
            strategySuggestions.addAll(generateQualityImprovementSuggestions(
                reflection.getQualityAssessment(), config));
        }

        // 基于策略效果分析生成改进建议
        if (reflection.getStrategyAnalysis().getOverallEffectiveness() < config.getEffectivenessThreshold()) {
            strategySuggestions.addAll(generateStrategyImprovementSuggestions(
                reflection.getStrategyAnalysis(), config));
        }

        // 生成参数优化建议
        parameterSuggestions.addAll(generateParameterOptimizationSuggestions(
            reflection, config));

        return ImprovementSuggestions.builder()
            .strategySuggestions(strategySuggestions)
            .parameterSuggestions(parameterSuggestions)
            .priorityLevel(calculatePriorityLevel(strategySuggestions, parameterSuggestions))
            .build();
    }

    @Override
    public ParameterAdjustment adjustParameters(ImprovementSuggestions suggestions,
                                              ParameterAdjustmentConfig config) {
        Map<String, Object> adjustedParameters = new HashMap<>();
        List<String> adjustmentReasons = new ArrayList<>();

        // 处理参数优化建议
        for (ParameterSuggestion suggestion : suggestions.getParameterSuggestions()) {
            if (suggestion.getConfidence() > config.getMinConfidence()) {
                adjustedParameters.put(suggestion.getParameterName(), suggestion.getSuggestedValue());
                adjustmentReasons.add(suggestion.getReason());
            }
        }

        // 使用参数优化器进行智能调整
        Map<String, Object> optimizedParameters = parameterOptimizer.optimize(
            adjustedParameters, config.getOptimizationConfig());

        return ParameterAdjustment.builder()
            .originalParameters(config.getCurrentParameters())
            .adjustedParameters(optimizedParameters)
            .adjustmentReasons(adjustmentReasons)
            .adjustmentConfidence(calculateAdjustmentConfidence(suggestions))
            .build();
    }

    private String buildQualityAssessmentPrompt(List<RankedResult> results,
                                              SearchQuery query,
                                              QualityAssessmentConfig config) {
        StringBuilder resultsText = new StringBuilder();
        for (int i = 0; i < Math.min(results.size(), 5); i++) {
            resultsText.append(String.format("%d. %s\n", i + 1, results.get(i).getContent()));
        }

        return String.format(
            "请对以下检索结果进行质量评估：\n\n" +
            "原始查询：%s\n\n" +
            "检索结果：\n%s\n" +
            "请从以下维度进行评估：\n" +
            "1. 相关性：结果与查询的相关程度\n" +
            "2. 完整性：结果是否完整回答了查询\n" +
            "3. 准确性：结果信息的准确性和可靠性\n" +
            "4. 多样性：结果的多样性和覆盖面\n" +
            "5. 时效性：结果信息的时效性\n\n" +
            "请为每个维度打分（1-10分）并提供详细的评估理由。",
            query.getText(),
            resultsText.toString()
        );
    }
}
```

### 3. 查询重写服务架构

#### 1.1 查询重写服务接口
```java
public interface QueryRewriteService {
    List<RewrittenQuery> rewriteQuery(SearchQuery originalQuery, QueryRewriteConfig config);
    RewrittenQuery expandQuery(SearchQuery query, QueryExpansionConfig config);
    RewrittenQuery reformulateQuery(SearchQuery query, QueryReformulationConfig config);
    List<RewrittenQuery> decomposeQuery(SearchQuery query, QueryDecompositionConfig config);
    List<RewrittenQuery> generateMultiQueries(SearchQuery query, MultiQueryConfig config);
}

@Data
@Builder
public class RewrittenQuery {
    private String originalQuery;
    private String rewrittenQuery;
    private QueryRewriteType type;
    private double confidence;
    private Map<String, Object> metadata;
    private List<String> addedTerms;
    private List<String> removedTerms;
    private long processingTime;
}

public enum QueryRewriteType {
    EXPANSION,           // 查询扩展
    REFORMULATION,       // 查询改写
    DECOMPOSITION,       // 查询分解
    MULTI_QUERY,         // 多查询生成
    SIMPLIFICATION,      // 查询简化
    SPECIALIZATION       // 查询专业化
}
```

#### 1.2 查询重写服务实现
```java
@Component
public class QueryRewriteServiceImpl implements QueryRewriteService {

    @Autowired
    private QueryExpansionService queryExpansionService;

    @Autowired
    private LLMService llmService;

    @Autowired
    private SynonymService synonymService;

    @Override
    public List<RewrittenQuery> rewriteQuery(SearchQuery originalQuery, QueryRewriteConfig config) {
        List<RewrittenQuery> rewrittenQueries = new ArrayList<>();

        // 1. 查询扩展
        if (config.isEnableExpansion()) {
            RewrittenQuery expandedQuery = expandQuery(originalQuery, config.getExpansionConfig());
            if (expandedQuery != null && expandedQuery.getConfidence() > config.getMinConfidence()) {
                rewrittenQueries.add(expandedQuery);
            }
        }

        // 2. 查询改写
        if (config.isEnableReformulation()) {
            RewrittenQuery reformulatedQuery = reformulateQuery(originalQuery, config.getReformulationConfig());
            if (reformulatedQuery != null && reformulatedQuery.getConfidence() > config.getMinConfidence()) {
                rewrittenQueries.add(reformulatedQuery);
            }
        }

        // 3. 查询分解
        if (config.isEnableDecomposition() && isComplexQuery(originalQuery.getText())) {
            List<RewrittenQuery> decomposedQueries = decomposeQuery(originalQuery, config.getDecompositionConfig());
            rewrittenQueries.addAll(decomposedQueries);
        }

        // 4. 多查询生成
        if (config.isEnableMultiQuery()) {
            List<RewrittenQuery> multiQueries = generateMultiQueries(originalQuery, config.getMultiQueryConfig());
            rewrittenQueries.addAll(multiQueries);
        }

        // 5. 查询去重和排序
        return deduplicateAndRank(rewrittenQueries, config);
    }

    @Override
    public RewrittenQuery expandQuery(SearchQuery query, QueryExpansionConfig config) {
        long startTime = System.currentTimeMillis();

        // 同义词扩展
        Set<String> synonyms = synonymService.getSynonyms(query.getText(), config.getLanguage());

        // 相关词扩展
        Set<String> relatedTerms = queryExpansionService.getRelatedTerms(query.getText(), config);

        // 构建扩展查询
        StringBuilder expandedQuery = new StringBuilder(query.getText());
        Set<String> addedTerms = new HashSet<>();

        // 添加同义词（权重较高）
        synonyms.stream()
            .limit(config.getMaxSynonyms())
            .forEach(synonym -> {
                expandedQuery.append(" ").append(synonym);
                addedTerms.add(synonym);
            });

        // 添加相关词（权重较低）
        relatedTerms.stream()
            .limit(config.getMaxRelatedTerms())
            .forEach(relatedTerm -> {
                expandedQuery.append(" ").append(relatedTerm);
                addedTerms.add(relatedTerm);
            });

        double confidence = calculateExpansionConfidence(addedTerms, query.getText(), config);

        return RewrittenQuery.builder()
            .originalQuery(query.getText())
            .rewrittenQuery(expandedQuery.toString().trim())
            .type(QueryRewriteType.EXPANSION)
            .confidence(confidence)
            .addedTerms(new ArrayList<>(addedTerms))
            .processingTime(System.currentTimeMillis() - startTime)
            .metadata(Map.of("synonymCount", synonyms.size(), "relatedTermCount", relatedTerms.size()))
            .build();
    }

    @Override
    public RewrittenQuery reformulateQuery(SearchQuery query, QueryReformulationConfig config) {
        long startTime = System.currentTimeMillis();

        // 构建改写提示词
        String prompt = buildReformulationPrompt(query.getText(), config);

        // 使用LLM进行查询改写
        String reformulatedQuery = llmService.generateText(prompt, config.getLlmConfig());

        // 清理和验证改写结果
        reformulatedQuery = cleanReformulatedQuery(reformulatedQuery);
        double confidence = validateReformulation(query.getText(), reformulatedQuery, config);

        if (confidence < config.getMinConfidence()) {
            return null; // 改写质量不够
        }

        return RewrittenQuery.builder()
            .originalQuery(query.getText())
            .rewrittenQuery(reformulatedQuery)
            .type(QueryRewriteType.REFORMULATION)
            .confidence(confidence)
            .processingTime(System.currentTimeMillis() - startTime)
            .metadata(Map.of("llmModel", config.getLlmConfig().getModelName()))
            .build();
    }

    private String buildReformulationPrompt(String originalQuery, QueryReformulationConfig config) {
        return String.format(
            "请将以下查询改写为更清晰、更准确的表达方式，保持原意不变：\n" +
            "原查询：%s\n" +
            "改写要求：%s\n" +
            "请只返回改写后的查询，不要包含其他内容。",
            originalQuery,
            config.getReformulationInstructions()
        );
    }

    private boolean isComplexQuery(String query) {
        // 判断查询是否复杂（包含多个概念、连接词等）
        String[] complexIndicators = {"和", "或", "以及", "关于", "如何", "什么", "为什么"};
        return Arrays.stream(complexIndicators).anyMatch(query::contains) ||
               query.split("\\s+").length > 5;
    }
}
```

### 2. 多阶段检索架构

#### 1.1 检索管道设计
```java
@Component
public class MultiStageRetrievalPipeline {

    @Autowired
    private CoTReasoningService cotReasoningService;

    @Autowired
    private SelfReflectionService selfReflectionService;

    @Autowired
    private QueryRewriteService queryRewriteService;

    @Autowired
    private MilvusHybridSearchService hybridSearchService;

    @Autowired
    private RerankingService rerankingService;

    @Autowired
    private DiversityOptimizer diversityOptimizer;

    public RetrievalResult retrieve(SearchQuery query, RetrievalConfig config) {
        long startTime = System.currentTimeMillis();

        // 第零阶段：CoT推理链
        ReasoningChain reasoningChain = cotReasoning(query, config);

        // 第一阶段：查询重写（基于CoT推理结果）
        List<RewrittenQuery> rewrittenQueries = queryRewrite(query, reasoningChain, config);

        // 第二阶段：混合检索
        List<Candidate> coarseCandidates = hybridRetrieval(rewrittenQueries, config);

        // 第三阶段：精排重排
        List<RankedResult> rankedResults = fineRanking(coarseCandidates, query, config);

        // 第四阶段：多样性优化
        List<RankedResult> diversifiedResults = diversityOptimization(rankedResults, config);

        // 第五阶段：后处理过滤
        List<RankedResult> finalResults = postProcessing(diversifiedResults, query, config);

        // 构建初步检索结果
        RetrievalResult initialResult = RetrievalResult.builder()
            .results(finalResults)
            .totalCandidates(coarseCandidates.size())
            .rewrittenQueries(rewrittenQueries.stream()
                .map(RewrittenQuery::getRewrittenQuery)
                .collect(Collectors.toList()))
            .query(query)
            .processingTime(System.currentTimeMillis() - startTime)
            .reasoningChain(reasoningChain)
            .build();

        // 第六阶段：自我反思机制
        ReflectionResult reflectionResult = selfReflection(initialResult, reasoningChain, config);

        // 根据反思结果决定是否重新检索
        if (reflectionResult.isNeedsReretrieval() && config.isEnableReretrieval()) {
            return reRetrieve(query, reflectionResult, config);
        }

        // 应用参数调整并返回最终结果
        return applyReflectionResults(initialResult, reflectionResult);
    }

    private ReasoningChain cotReasoning(SearchQuery query, RetrievalConfig config) {
        if (!config.isEnableCoTReasoning()) {
            return null;
        }

        return cotReasoningService.analyzeQuery(query, config.getCotConfig());
    }

    private List<RewrittenQuery> queryRewrite(SearchQuery query,
                                            ReasoningChain reasoningChain,
                                            RetrievalConfig config) {
        if (!config.isEnableQueryRewrite()) {
            return Collections.singletonList(RewrittenQuery.builder()
                .originalQuery(query.getText())
                .rewrittenQuery(query.getText())
                .type(QueryRewriteType.ORIGINAL)
                .confidence(1.0)
                .build());
        }

        // 基于CoT推理结果优化查询重写配置
        QueryRewriteConfig optimizedConfig = optimizeQueryRewriteConfig(
            config.getQueryRewrite(), reasoningChain);

        List<RewrittenQuery> rewrittenQueries = queryRewriteService.rewriteQuery(
            query, optimizedConfig);

        // 确保至少包含原始查询
        if (rewrittenQueries.isEmpty()) {
            rewrittenQueries.add(RewrittenQuery.builder()
                .originalQuery(query.getText())
                .rewrittenQuery(query.getText())
                .type(QueryRewriteType.ORIGINAL)
                .confidence(1.0)
                .build());
        }

        return rewrittenQueries;
    }

    private ReflectionResult selfReflection(RetrievalResult retrievalResult,
                                          ReasoningChain reasoningChain,
                                          RetrievalConfig config) {
        if (!config.isEnableSelfReflection()) {
            return ReflectionResult.builder()
                .needsReretrieval(false)
                .overallSatisfaction(1.0)
                .build();
        }

        return selfReflectionService.reflect(
            retrievalResult, reasoningChain, config.getReflectionConfig());
    }

    private RetrievalResult reRetrieve(SearchQuery query,
                                     ReflectionResult reflectionResult,
                                     RetrievalConfig config) {
        // 应用参数调整
        RetrievalConfig adjustedConfig = applyParameterAdjustments(config, reflectionResult);

        // 记录重新检索的原因
        log.info("Re-retrieving due to reflection results. Satisfaction: {}, Adjustments: {}",
            reflectionResult.getOverallSatisfaction(),
            reflectionResult.getParameterAdjustment().getAdjustmentReasons());

        // 递归调用检索（限制递归深度）
        if (adjustedConfig.getRetrievalDepth() < adjustedConfig.getMaxRetrievalDepth()) {
            adjustedConfig.setRetrievalDepth(adjustedConfig.getRetrievalDepth() + 1);
            return retrieve(query, adjustedConfig);
        } else {
            log.warn("Maximum retrieval depth reached, returning original results");
            return RetrievalResult.builder()
                .results(Collections.emptyList())
                .query(query)
                .build();
        }
    }

    private RetrievalResult applyReflectionResults(RetrievalResult originalResult,
                                                 ReflectionResult reflectionResult) {
        return RetrievalResult.builder()
            .results(originalResult.getResults())
            .totalCandidates(originalResult.getTotalCandidates())
            .rewrittenQueries(originalResult.getRewrittenQueries())
            .query(originalResult.getQuery())
            .processingTime(originalResult.getProcessingTime())
            .reasoningChain(originalResult.getReasoningChain())
            .reflectionResult(reflectionResult)
            .qualityScore(reflectionResult.getQualityAssessment().getOverallQualityScore())
            .satisfactionScore(reflectionResult.getOverallSatisfaction())
            .build();
    }

    private QueryRewriteConfig optimizeQueryRewriteConfig(QueryRewriteConfig originalConfig,
                                                        ReasoningChain reasoningChain) {
        if (reasoningChain == null) {
            return originalConfig;
        }

        QueryRewriteConfig optimizedConfig = originalConfig.copy();

        // 基于意图分析调整重写策略
        String intent = reasoningChain.getIntentAnalysis().getIntent();
        if ("FACTUAL".equals(intent)) {
            // 事实性查询更注重精确性
            optimizedConfig.getExpansionConfig().setMaxSynonyms(2);
            optimizedConfig.setEnableDecomposition(false);
        } else if ("ANALYTICAL".equals(intent)) {
            // 分析性查询更注重全面性
            optimizedConfig.getExpansionConfig().setMaxRelatedTerms(3);
            optimizedConfig.setEnableDecomposition(true);
        }

        // 基于概念复杂度调整参数
        int conceptCount = reasoningChain.getConceptExtraction().getConcepts().size();
        if (conceptCount > 5) {
            optimizedConfig.setMaxRewrittenQueries(conceptCount);
            optimizedConfig.setEnableMultiQuery(true);
        }

        return optimizedConfig;
    }

    private RetrievalConfig applyParameterAdjustments(RetrievalConfig originalConfig,
                                                    ReflectionResult reflectionResult) {
        RetrievalConfig adjustedConfig = originalConfig.copy();

        // 应用参数调整建议
        Map<String, Object> adjustments = reflectionResult.getParameterAdjustment().getAdjustedParameters();

        for (Map.Entry<String, Object> adjustment : adjustments.entrySet()) {
            String paramName = adjustment.getKey();
            Object paramValue = adjustment.getValue();

            // 应用具体的参数调整
            applyParameterAdjustment(adjustedConfig, paramName, paramValue);
        }

        return adjustedConfig;
    }

    private void applyParameterAdjustment(RetrievalConfig config, String paramName, Object paramValue) {
        switch (paramName) {
            case "coarseTopK":
                config.setCoarseTopK((Integer) paramValue);
                break;
            case "finalTopK":
                config.setFinalTopK((Integer) paramValue);
                break;
            case "diversityThreshold":
                config.getDiversity().setDiversityThreshold((Double) paramValue);
                break;
            case "relevanceThreshold":
                config.setRelevanceThreshold((Double) paramValue);
                break;
            // 添加更多参数调整逻辑
            default:
                log.warn("Unknown parameter for adjustment: {}", paramName);
        }
    }

        return RetrievalResult.builder()
            .results(finalResults)
            .totalCandidates(coarseCandidates.size())
            .rewrittenQueries(rewrittenQueries.stream()
                .map(RewrittenQuery::getRewrittenQuery)
                .collect(Collectors.toList()))
            .processingTime(System.currentTimeMillis())
            .query(query)
            .build();
    }

    private List<RewrittenQuery> queryRewrite(SearchQuery query, RetrievalConfig config) {
        if (!config.isEnableQueryRewrite()) {
            // 如果未启用查询重写，返回原始查询
            return Collections.singletonList(RewrittenQuery.builder()
                .originalQuery(query.getText())
                .rewrittenQuery(query.getText())
                .type(QueryRewriteType.ORIGINAL)
                .confidence(1.0)
                .build());
        }

        List<RewrittenQuery> rewrittenQueries = queryRewriteService.rewriteQuery(
            query, config.getQueryRewriteConfig());

        // 确保至少包含原始查询
        if (rewrittenQueries.isEmpty()) {
            rewrittenQueries.add(RewrittenQuery.builder()
                .originalQuery(query.getText())
                .rewrittenQuery(query.getText())
                .type(QueryRewriteType.ORIGINAL)
                .confidence(1.0)
                .build());
        }

        return rewrittenQueries;
    }

    private List<Candidate> hybridRetrieval(List<RewrittenQuery> rewrittenQueries, RetrievalConfig config) {
        List<Candidate> allCandidates = new ArrayList<>();

        for (RewrittenQuery rewrittenQuery : rewrittenQueries) {
            // 为每个重写查询创建SearchQuery对象
            SearchQuery searchQuery = SearchQuery.builder()
                .text(rewrittenQuery.getRewrittenQuery())
                .language(config.getLanguage())
                .build();

            // 使用Milvus混合检索
            HybridSearchResult hybridResult = hybridSearchService.hybridSearch(
                searchQuery, config.getHybridSearchConfig());

            // 转换为Candidate并添加重写信息
            List<Candidate> candidates = hybridResult.getCandidates().stream()
                .map(hybridCandidate -> Candidate.builder()
                    .documentId(hybridCandidate.getDocumentId())
                    .content(hybridCandidate.getContent())
                    .score(hybridCandidate.getScore() * rewrittenQuery.getConfidence()) // 乘以查询置信度
                    .metadata(hybridCandidate.getMetadata())
                    .source("hybrid_" + rewrittenQuery.getType().name().toLowerCase())
                    .build())
                .collect(Collectors.toList());

            allCandidates.addAll(candidates);
        }

        // 合并去重和重新排序
        return mergeCandidates(allCandidates, config.getCoarseTopK());
    }
    
    private List<RankedResult> fineRanking(List<Candidate> candidates, 
                                         SearchQuery query, 
                                         RetrievalConfig config) {
        if (config.isEnableReranking()) {
            return rerankingService.rerank(candidates, query, config.getRerankingConfig());
        } else {
            return candidates.stream()
                .map(this::convertToRankedResult)
                .collect(Collectors.toList());
        }
    }
    
    private List<RankedResult> diversityOptimization(List<RankedResult> results, 
                                                   RetrievalConfig config) {
        if (config.isEnableDiversityOptimization()) {
            return diversityOptimizer.optimize(results, config.getDiversityConfig());
        }
        return results;
    }
}
```

#### 1.2 向量检索服务
```java
@Component
public class VectorSearchService {
    
    @Autowired
    private MilvusClient milvusClient;
    
    @Autowired
    private EmbeddingService embeddingService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public List<VectorCandidate> search(SearchQuery query, VectorSearchConfig config) {
        // 查询缓存
        String cacheKey = generateCacheKey(query, config);
        List<VectorCandidate> cachedResults = getCachedResults(cacheKey);
        if (cachedResults != null && config.isEnableCaching()) {
            return cachedResults;
        }
        
        // 查询向量化
        float[] queryVector = embeddingService.generateEmbedding(
            query.getText(), config.getEmbeddingConfig());
        
        // 构建搜索参数
        SearchParam searchParam = buildSearchParam(query, queryVector, config);
        
        // 执行向量搜索
        SearchResult searchResult = milvusClient.search(searchParam);
        
        // 转换结果
        List<VectorCandidate> candidates = convertSearchResult(searchResult, query);
        
        // 缓存结果
        if (config.isEnableCaching()) {
            cacheResults(cacheKey, candidates, config.getCacheExpiration());
        }
        
        return candidates;
    }
    
    private SearchParam buildSearchParam(SearchQuery query, float[] queryVector, 
                                       VectorSearchConfig config) {
        return SearchParam.builder()
            .collectionName(getCollectionName(query.getKnowledgeBaseId()))
            .vectors(Collections.singletonList(queryVector))
            .topK(config.getTopK())
            .metricType(config.getMetricType())
            .searchParams(buildMilvusSearchParams(config))
            .expr(buildFilterExpression(query, config))
            .outputFields(Arrays.asList("content", "metadata", "chunk_id", "document_id"))
            .build();
    }
    
    private String buildFilterExpression(SearchQuery query, VectorSearchConfig config) {
        List<String> conditions = new ArrayList<>();
        
        // 知识库过滤
        conditions.add("knowledge_base_id == " + query.getKnowledgeBaseId());
        
        // 时间范围过滤
        if (query.getTimeRange() != null) {
            conditions.add("created_time >= " + query.getTimeRange().getStart());
            conditions.add("created_time <= " + query.getTimeRange().getEnd());
        }
        
        // 文档类型过滤
        if (query.getDocumentTypes() != null && !query.getDocumentTypes().isEmpty()) {
            String typeFilter = query.getDocumentTypes().stream()
                .map(type -> "\"" + type + "\"")
                .collect(Collectors.joining(", "));
            conditions.add("document_type in [" + typeFilter + "]");
        }
        
        // 自定义过滤条件
        if (config.getCustomFilters() != null) {
            conditions.addAll(config.getCustomFilters());
        }
        
        return String.join(" && ", conditions);
    }
    
    private List<VectorCandidate> convertSearchResult(SearchResult searchResult, SearchQuery query) {
        List<VectorCandidate> candidates = new ArrayList<>();
        
        for (SearchResultItem item : searchResult.getItems()) {
            VectorCandidate candidate = VectorCandidate.builder()
                .chunkId(item.getLongField("chunk_id"))
                .documentId(item.getLongField("document_id"))
                .content(item.getStringField("content"))
                .score(item.getScore())
                .distance(item.getDistance())
                .metadata(parseMetadata(item.getStringField("metadata")))
                .searchMethod(SearchMethod.VECTOR)
                .build();
            
            candidates.add(candidate);
        }
        
        return candidates;
    }
}
```

#### 1.3 关键词检索服务
```java
@Component
public class KeywordSearchService {
    
    @Autowired
    private ElasticsearchClient elasticsearchClient;
    
    @Autowired
    private QueryExpansionService queryExpansionService;
    
    public List<KeywordCandidate> search(SearchQuery query, KeywordSearchConfig config) {
        // 查询扩展
        ExpandedQuery expandedQuery = queryExpansionService.expand(query, config);
        
        // 构建ES查询
        SearchRequest searchRequest = buildElasticsearchQuery(expandedQuery, config);
        
        // 执行搜索
        SearchResponse<DocumentChunk> response = elasticsearchClient.search(
            searchRequest, DocumentChunk.class);
        
        // 转换结果
        return convertElasticsearchResult(response, query);
    }
    
    private SearchRequest buildElasticsearchQuery(ExpandedQuery expandedQuery, 
                                                KeywordSearchConfig config) {
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();
        
        // 主查询
        boolQuery.must(buildMainQuery(expandedQuery, config));
        
        // 过滤条件
        boolQuery.filter(buildFilters(expandedQuery, config));
        
        // 构建搜索请求
        return SearchRequest.of(s -> s
            .index(getIndexName(expandedQuery.getKnowledgeBaseId()))
            .query(boolQuery.build()._toQuery())
            .size(config.getTopK())
            .highlight(buildHighlight(config))
            .sort(buildSort(config))
        );
    }
    
    private Query buildMainQuery(ExpandedQuery expandedQuery, KeywordSearchConfig config) {
        MultiMatchQuery.Builder multiMatch = new MultiMatchQuery.Builder()
            .query(expandedQuery.getOriginalText())
            .fields(Arrays.asList("content^2.0", "title^3.0", "summary^1.5"))
            .type(TextQueryType.BestFields)
            .fuzziness("AUTO");
        
        // 添加同义词查询
        if (!expandedQuery.getSynonyms().isEmpty()) {
            BoolQuery.Builder synonymQuery = new BoolQuery.Builder();
            for (String synonym : expandedQuery.getSynonyms()) {
                synonymQuery.should(MatchQuery.of(m -> m
                    .field("content")
                    .query(synonym)
                    .boost(0.8f)
                )._toQuery());
            }
            
            return BoolQuery.of(b -> b
                .must(multiMatch.build()._toQuery())
                .should(synonymQuery.build()._toQuery())
            )._toQuery();
        }
        
        return multiMatch.build()._toQuery();
    }
    
    private List<KeywordCandidate> convertElasticsearchResult(SearchResponse<DocumentChunk> response, 
                                                            SearchQuery query) {
        List<KeywordCandidate> candidates = new ArrayList<>();
        
        for (Hit<DocumentChunk> hit : response.hits().hits()) {
            DocumentChunk chunk = hit.source();
            
            KeywordCandidate candidate = KeywordCandidate.builder()
                .chunkId(chunk.getId())
                .documentId(chunk.getDocumentId())
                .content(chunk.getContent())
                .score(hit.score())
                .highlights(extractHighlights(hit.highlight()))
                .searchMethod(SearchMethod.KEYWORD)
                .bm25Score(calculateBM25Score(hit, query))
                .build();
            
            candidates.add(candidate);
        }
        
        return candidates;
    }
}
```

### 2. Milvus混合检索策略

#### 2.1 Milvus混合检索服务
```java
@Component
public class MilvusHybridSearchService {

    @Autowired
    private MilvusClient milvusClient;

    @Autowired
    private DenseVectorService denseVectorService;

    @Autowired
    private SparseVectorService sparseVectorService;

    public HybridSearchResult hybridSearch(SearchQuery query, HybridSearchConfig config) {
        // 生成稠密向量和稀疏向量
        float[] denseVector = denseVectorService.generateDenseVector(query.getText());
        SparseFloatVector sparseVector = sparseVectorService.generateSparseVector(query.getText());

        // 构建Milvus混合检索请求
        HybridSearchReq hybridSearchReq = HybridSearchReq.builder()
            .collectionName(config.getCollectionName())
            .searchRequests(buildSearchRequests(denseVector, sparseVector, config))
            .ranker(RRFRanker.builder()
                .k(config.getRrfK())
                .build())
            .topK(config.getTopK())
            .build();

        // 执行混合检索
        SearchResp searchResp = milvusClient.hybridSearch(hybridSearchReq);

        return processHybridSearchResults(searchResp, query, config);
    }

    private List<SearchReq> buildSearchRequests(float[] denseVector,
                                              SparseFloatVector sparseVector,
                                              HybridSearchConfig config) {
        List<SearchReq> searchRequests = new ArrayList<>();

        // 稠密向量检索请求
        SearchReq denseSearchReq = SearchReq.builder()
            .collectionName(config.getCollectionName())
            .data(Collections.singletonList(denseVector))
            .annsField(config.getDenseVectorField())
            .topK(config.getDenseTopK())
            .metricType(MetricType.COSINE)
            .params(config.getDenseSearchParams())
            .build();
        searchRequests.add(denseSearchReq);

        // 稀疏向量检索请求
        SearchReq sparseSearchReq = SearchReq.builder()
            .collectionName(config.getCollectionName())
            .data(Collections.singletonList(sparseVector))
            .annsField(config.getSparseVectorField())
            .topK(config.getSparseTopK())
            .metricType(MetricType.IP)
            .params(config.getSparseSearchParams())
            .build();
        searchRequests.add(sparseSearchReq);

        return searchRequests;
    }

    private HybridSearchResult processHybridSearchResults(SearchResp searchResp,
                                                        SearchQuery query,
                                                        HybridSearchConfig config) {
        List<HybridCandidate> candidates = new ArrayList<>();

        for (SearchResp.SearchResult result : searchResp.getSearchResults()) {
            for (int i = 0; i < result.getIds().size(); i++) {
                HybridCandidate candidate = HybridCandidate.builder()
                    .documentId(result.getIds().get(i).toString())
                    .score(result.getScores().get(i))
                    .content(extractContent(result, i))
                    .metadata(extractMetadata(result, i))
                    .rank(i + 1)
                    .build();
                candidates.add(candidate);
            }
        }

        return HybridSearchResult.builder()
            .candidates(candidates)
            .totalCount(candidates.size())
            .searchTime(System.currentTimeMillis())
            .query(query)
            .build();
    }
}
```

#### 2.2 稠密向量服务
```java
@Component
public class DenseVectorService {

    @Autowired
    private EmbeddingService embeddingService;

    public float[] generateDenseVector(String text) {
        // 使用向量化模块的服务生成稠密向量
        return embeddingService.generateEmbedding(text, EmbeddingConfig.defaultConfig());
    }

    public List<float[]> batchGenerateDenseVectors(List<String> texts) {
        return embeddingService.batchGenerateEmbedding(texts, EmbeddingConfig.defaultConfig());
    }
}
```

#### 2.3 稀疏向量服务
```java
@Component
public class SparseVectorService {

    @Autowired
    private SpladeModel spladeModel;

    @Autowired
    private BgeM3Model bgeM3Model;

    public SparseFloatVector generateSparseVector(String text) {
        // 使用SPLADE模型生成稀疏向量
        Map<Integer, Float> sparseMap = spladeModel.encode(text);

        // 转换为Milvus SparseFloatVector格式
        List<Long> indices = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        for (Map.Entry<Integer, Float> entry : sparseMap.entrySet()) {
            if (entry.getValue() > 0.01f) { // 过滤低权重
                indices.add(entry.getKey().longValue());
                values.add(entry.getValue());
            }
        }

        return new SparseFloatVector(indices, values);
    }

    public SparseFloatVector generateBgeM3SparseVector(String text) {
        // 使用BGE-M3模型生成稀疏向量
        Map<String, Float> tokenWeights = bgeM3Model.encodeSparse(text);

        List<Long> indices = new ArrayList<>();
        List<Float> values = new ArrayList<>();

        for (Map.Entry<String, Float> entry : tokenWeights.entrySet()) {
            if (entry.getValue() > 0.01f) {
                // 将token转换为索引
                Long tokenIndex = getTokenIndex(entry.getKey());
                if (tokenIndex != null) {
                    indices.add(tokenIndex);
                    values.add(entry.getValue());
                }
            }
        }

        return new SparseFloatVector(indices, values);
    }

    private Long getTokenIndex(String token) {
        // 实现token到索引的映射
        // 可以使用预构建的词汇表或哈希函数
        return (long) token.hashCode();
    }
}
```

#### 2.4 Milvus集合管理
```java
@Component
public class MilvusCollectionManager {

    @Autowired
    private MilvusClient milvusClient;

    public void createHybridCollection(String collectionName, HybridCollectionConfig config) {
        // 定义字段
        List<FieldType> fields = new ArrayList<>();

        // 主键字段
        fields.add(FieldType.newBuilder()
            .withName("id")
            .withDataType(DataType.VarChar)
            .withMaxLength(65535)
            .withIsPrimaryKey(true)
            .build());

        // 稠密向量字段
        fields.add(FieldType.newBuilder()
            .withName(config.getDenseVectorField())
            .withDataType(DataType.FloatVector)
            .withDimension(config.getDenseVectorDimension())
            .build());

        // 稀疏向量字段
        fields.add(FieldType.newBuilder()
            .withName(config.getSparseVectorField())
            .withDataType(DataType.SparseFloatVector)
            .build());

        // 文本内容字段
        fields.add(FieldType.newBuilder()
            .withName("content")
            .withDataType(DataType.VarChar)
            .withMaxLength(65535)
            .build());

        // 元数据字段
        fields.add(FieldType.newBuilder()
            .withName("metadata")
            .withDataType(DataType.JSON)
            .build());

        // 创建集合
        CreateCollectionReq createCollectionReq = CreateCollectionReq.builder()
            .collectionName(collectionName)
            .collectionSchema(CollectionSchema.newBuilder()
                .withFieldTypes(fields)
                .build())
            .build();

        milvusClient.createCollection(createCollectionReq);

        // 创建索引
        createHybridIndexes(collectionName, config);
    }

    private void createHybridIndexes(String collectionName, HybridCollectionConfig config) {
        // 创建稠密向量索引
        CreateIndexReq denseIndexReq = CreateIndexReq.builder()
            .collectionName(collectionName)
            .fieldName(config.getDenseVectorField())
            .indexType(IndexType.HNSW)
            .metricType(MetricType.COSINE)
            .extraParams(Map.of(
                "M", 16,
                "efConstruction", 200
            ))
            .build();
        milvusClient.createIndex(denseIndexReq);

        // 创建稀疏向量索引
        CreateIndexReq sparseIndexReq = CreateIndexReq.builder()
            .collectionName(collectionName)
            .fieldName(config.getSparseVectorField())
            .indexType(IndexType.SPARSE_INVERTED_INDEX)
            .metricType(MetricType.IP)
            .extraParams(Map.of(
                "drop_ratio_build", 0.2
            ))
            .build();
        milvusClient.createIndex(sparseIndexReq);
    }

    public void insertHybridData(String collectionName, List<HybridDocument> documents) {
        List<JsonObject> data = new ArrayList<>();

        for (HybridDocument doc : documents) {
            JsonObject row = new JsonObject();
            row.addProperty("id", doc.getId());
            row.add(doc.getDenseVectorField(), gson.toJsonTree(doc.getDenseVector()));
            row.add(doc.getSparseVectorField(), gson.toJsonTree(doc.getSparseVector()));
            row.addProperty("content", doc.getContent());
            row.add("metadata", gson.toJsonTree(doc.getMetadata()));
            data.add(row);
        }

        InsertReq insertReq = InsertReq.builder()
            .collectionName(collectionName)
            .data(data)
            .build();

        milvusClient.insert(insertReq);
    }
}
        
        // 处理向量检索结果
        for (VectorCandidate vectorCandidate : vectorResults) {
            Long chunkId = vectorCandidate.getChunkId();
            
            if (candidateMap.containsKey(chunkId)) {
                // 合并分数
                Candidate existing = candidateMap.get(chunkId);
                double fusedScore = fuseScores(
                    existing.getScore(), 
                    vectorCandidate.getScore(), 
                    config.getVectorWeight(),
                    config.getKeywordWeight()
                );
                existing.setScore(fusedScore);
                existing.addSearchMethod(SearchMethod.VECTOR);
            } else {
                Candidate candidate = convertToCandidate(vectorCandidate);
                candidateMap.put(chunkId, candidate);
            }
        }
        
        // 处理关键词检索结果
        for (KeywordCandidate keywordCandidate : keywordResults) {
            Long chunkId = keywordCandidate.getChunkId();
            
            if (candidateMap.containsKey(chunkId)) {
                // 合并分数
                Candidate existing = candidateMap.get(chunkId);
                double fusedScore = fuseScores(
                    existing.getScore(),
                    keywordCandidate.getScore(),
                    config.getVectorWeight(),
                    config.getKeywordWeight()
                );
                existing.setScore(fusedScore);
                existing.addSearchMethod(SearchMethod.KEYWORD);
            } else {
                Candidate candidate = convertToCandidate(keywordCandidate);
                candidateMap.put(chunkId, candidate);
            }
        }
        
        // 排序并返回
        return candidateMap.values().stream()
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .collect(Collectors.toList());
    }
    
    private double fuseScores(double existingScore, double newScore, 
                            double vectorWeight, double keywordWeight) {
        // 使用加权平均融合分数
        return existingScore * 0.5 + newScore * 0.5;
    }
    
    // RRF (Reciprocal Rank Fusion) 算法
    public List<Candidate> fuseWithRRF(List<VectorCandidate> vectorResults,
                                      List<KeywordCandidate> keywordResults,
                                      int k) {
        Map<Long, Double> rrfScores = new HashMap<>();
        Map<Long, Candidate> candidateMap = new HashMap<>();
        
        // 计算向量检索的RRF分数
        for (int i = 0; i < vectorResults.size(); i++) {
            VectorCandidate candidate = vectorResults.get(i);
            Long chunkId = candidate.getChunkId();
            double rrfScore = 1.0 / (k + i + 1);
            
            rrfScores.merge(chunkId, rrfScore, Double::sum);
            candidateMap.putIfAbsent(chunkId, convertToCandidate(candidate));
        }
        
        // 计算关键词检索的RRF分数
        for (int i = 0; i < keywordResults.size(); i++) {
            KeywordCandidate candidate = keywordResults.get(i);
            Long chunkId = candidate.getChunkId();
            double rrfScore = 1.0 / (k + i + 1);
            
            rrfScores.merge(chunkId, rrfScore, Double::sum);
            candidateMap.putIfAbsent(chunkId, convertToCandidate(candidate));
        }
        
        // 按RRF分数排序
        return rrfScores.entrySet().stream()
            .sorted(Map.Entry.<Long, Double>comparingByValue().reversed())
            .map(entry -> {
                Candidate candidate = candidateMap.get(entry.getKey());
                candidate.setScore(entry.getValue());
                return candidate;
            })
            .collect(Collectors.toList());
    }
}
```

#### 2.2 查询扩展服务
```java
@Component
public class QueryExpansionService {
    
    @Autowired
    private SynonymService synonymService;
    
    @Autowired
    private AliCloudNlpClient nlpClient;
    
    public ExpandedQuery expand(SearchQuery query, KeywordSearchConfig config) {
        String originalText = query.getText();
        
        ExpandedQuery.Builder builder = ExpandedQuery.builder()
            .originalText(originalText)
            .knowledgeBaseId(query.getKnowledgeBaseId());
        
        // 同义词扩展
        if (config.isEnableSynonymExpansion()) {
            List<String> synonyms = synonymService.getSynonyms(originalText);
            builder.synonyms(synonyms);
        }
        
        // 关键词提取
        if (config.isEnableKeywordExtraction()) {
            List<String> keywords = nlpClient.extractKeywords(originalText);
            builder.keywords(keywords);
        }
        
        // 查询重写
        if (config.isEnableQueryRewriting()) {
            List<String> rewrittenQueries = generateQueryRewrites(originalText);
            builder.rewrittenQueries(rewrittenQueries);
        }
        
        // 实体识别
        if (config.isEnableEntityRecognition()) {
            List<Entity> entities = nlpClient.recognizeEntities(originalText);
            builder.entities(entities);
        }
        
        return builder.build();
    }
    
    private List<String> generateQueryRewrites(String originalQuery) {
        List<String> rewrites = new ArrayList<>();
        
        // 基于模板的重写
        rewrites.add("如何" + originalQuery);
        rewrites.add("什么是" + originalQuery);
        rewrites.add(originalQuery + "的方法");
        rewrites.add(originalQuery + "的原理");
        
        // 基于NLP的重写
        List<String> nlpRewrites = nlpClient.rewriteQuery(originalQuery);
        rewrites.addAll(nlpRewrites);
        
        return rewrites.stream()
            .distinct()
            .limit(5)
            .collect(Collectors.toList());
    }
}
```

### 3. 高级检索技术

#### 3.1 重排序服务
```java
@Component
public class RerankingService {
    
    @Autowired
    private CrossEncoderService crossEncoderService;
    
    @Autowired
    private FeatureExtractor featureExtractor;
    
    public List<RankedResult> rerank(List<Candidate> candidates, 
                                   SearchQuery query, 
                                   RerankingConfig config) {
        if (candidates.size() <= config.getRerankThreshold()) {
            return candidates.stream()
                .map(this::convertToRankedResult)
                .collect(Collectors.toList());
        }
        
        switch (config.getRerankingMethod()) {
            case CROSS_ENCODER:
                return crossEncoderRerank(candidates, query, config);
            case FEATURE_BASED:
                return featureBasedRerank(candidates, query, config);
            case HYBRID:
                return hybridRerank(candidates, query, config);
            default:
                return candidates.stream()
                    .map(this::convertToRankedResult)
                    .collect(Collectors.toList());
        }
    }
    
    private List<RankedResult> crossEncoderRerank(List<Candidate> candidates,
                                                SearchQuery query,
                                                RerankingConfig config) {
        List<RerankingPair> pairs = candidates.stream()
            .map(candidate -> RerankingPair.builder()
                .query(query.getText())
                .document(candidate.getContent())
                .candidate(candidate)
                .build())
            .collect(Collectors.toList());
        
        // 批量计算相关性分数
        List<Double> relevanceScores = crossEncoderService.computeRelevanceScores(pairs);
        
        List<RankedResult> results = new ArrayList<>();
        for (int i = 0; i < candidates.size(); i++) {
            Candidate candidate = candidates.get(i);
            double relevanceScore = relevanceScores.get(i);
            
            // 融合原始分数和相关性分数
            double finalScore = config.getOriginalWeight() * candidate.getScore() +
                              config.getRelevanceWeight() * relevanceScore;
            
            RankedResult result = RankedResult.builder()
                .candidate(candidate)
                .originalScore(candidate.getScore())
                .relevanceScore(relevanceScore)
                .finalScore(finalScore)
                .rerankingMethod(RerankingMethod.CROSS_ENCODER)
                .build();
            
            results.add(result);
        }
        
        // 按最终分数排序
        return results.stream()
            .sorted((a, b) -> Double.compare(b.getFinalScore(), a.getFinalScore()))
            .collect(Collectors.toList());
    }
    
    private List<RankedResult> featureBasedRerank(List<Candidate> candidates,
                                                SearchQuery query,
                                                RerankingConfig config) {
        List<RankedResult> results = new ArrayList<>();
        
        for (Candidate candidate : candidates) {
            // 提取特征
            RerankingFeatures features = featureExtractor.extractFeatures(candidate, query);
            
            // 计算重排序分数
            double rerankScore = calculateRerankScore(features, config);
            
            RankedResult result = RankedResult.builder()
                .candidate(candidate)
                .originalScore(candidate.getScore())
                .rerankScore(rerankScore)
                .finalScore(rerankScore)
                .features(features)
                .rerankingMethod(RerankingMethod.FEATURE_BASED)
                .build();
            
            results.add(result);
        }
        
        return results.stream()
            .sorted((a, b) -> Double.compare(b.getFinalScore(), a.getFinalScore()))
            .collect(Collectors.toList());
    }
    
    private double calculateRerankScore(RerankingFeatures features, RerankingConfig config) {
        double score = 0.0;
        
        // 文本相似度特征
        score += features.getTextSimilarity() * config.getTextSimilarityWeight();
        
        // 语义相似度特征
        score += features.getSemanticSimilarity() * config.getSemanticSimilarityWeight();
        
        // 关键词匹配特征
        score += features.getKeywordMatchRatio() * config.getKeywordMatchWeight();
        
        // 文档质量特征
        score += features.getDocumentQuality() * config.getDocumentQualityWeight();
        
        // 新鲜度特征
        score += features.getFreshness() * config.getFreshnessWeight();
        
        return score;
    }
}
```

#### 3.2 多样性优化器
```java
@Component
public class DiversityOptimizer {
    
    public List<RankedResult> optimize(List<RankedResult> results, DiversityConfig config) {
        if (results.size() <= config.getMinResults()) {
            return results;
        }
        
        switch (config.getDiversityMethod()) {
            case MMR:
                return maximalMarginalRelevance(results, config);
            case CLUSTERING:
                return clusteringBasedDiversity(results, config);
            case TOPIC_DIVERSITY:
                return topicBasedDiversity(results, config);
            default:
                return results;
        }
    }
    
    private List<RankedResult> maximalMarginalRelevance(List<RankedResult> results, 
                                                      DiversityConfig config) {
        List<RankedResult> selected = new ArrayList<>();
        List<RankedResult> remaining = new ArrayList<>(results);
        
        // 选择第一个最相关的结果
        if (!remaining.isEmpty()) {
            RankedResult first = remaining.remove(0);
            selected.add(first);
        }
        
        // 迭代选择后续结果
        while (selected.size() < config.getMaxResults() && !remaining.isEmpty()) {
            RankedResult best = null;
            double bestScore = Double.NEGATIVE_INFINITY;
            
            for (RankedResult candidate : remaining) {
                // 计算MMR分数
                double relevanceScore = candidate.getFinalScore();
                double maxSimilarity = calculateMaxSimilarity(candidate, selected);
                
                double mmrScore = config.getLambda() * relevanceScore - 
                                (1 - config.getLambda()) * maxSimilarity;
                
                if (mmrScore > bestScore) {
                    bestScore = mmrScore;
                    best = candidate;
                }
            }
            
            if (best != null) {
                selected.add(best);
                remaining.remove(best);
            } else {
                break;
            }
        }
        
        return selected;
    }
    
    private double calculateMaxSimilarity(RankedResult candidate, List<RankedResult> selected) {
        if (selected.isEmpty()) {
            return 0.0;
        }
        
        return selected.stream()
            .mapToDouble(selected_result -> 
                calculateSimilarity(candidate.getCandidate().getContent(), 
                                  selected_result.getCandidate().getContent()))
            .max()
            .orElse(0.0);
    }
    
    private List<RankedResult> clusteringBasedDiversity(List<RankedResult> results, 
                                                      DiversityConfig config) {
        // 使用K-means聚类确保多样性
        List<float[]> embeddings = results.stream()
            .map(result -> embeddingService.generateEmbedding(
                result.getCandidate().getContent(), 
                EmbeddingConfig.defaultConfig()))
            .collect(Collectors.toList());
        
        // 执行聚类
        KMeansClusterer clusterer = new KMeansClusterer(config.getNumClusters());
        List<Cluster> clusters = clusterer.cluster(embeddings);
        
        // 从每个聚类中选择最佳结果
        List<RankedResult> diverseResults = new ArrayList<>();
        for (Cluster cluster : clusters) {
            List<Integer> indices = cluster.getIndices();
            
            // 选择聚类中分数最高的结果
            RankedResult best = indices.stream()
                .map(results::get)
                .max(Comparator.comparing(RankedResult::getFinalScore))
                .orElse(null);
            
            if (best != null) {
                diverseResults.add(best);
            }
        }
        
        // 按分数排序
        return diverseResults.stream()
            .sorted((a, b) -> Double.compare(b.getFinalScore(), a.getFinalScore()))
            .limit(config.getMaxResults())
            .collect(Collectors.toList());
    }
}
```

## 配置与监控

### 检索配置
```java
@ConfigurationProperties(prefix = "rag.retrieval")
@Data
public class RetrievalConfig {

    // 基础配置
    private int coarseTopK = 100;
    private int finalTopK = 10;
    private double relevanceThreshold = 0.6;

    // CoT推理配置
    private boolean enableCoTReasoning = true;
    private CoTConfig cotConfig = new CoTConfig();

    // 自我反思配置
    private boolean enableSelfReflection = true;
    private ReflectionConfig reflectionConfig = new ReflectionConfig();

    // 重新检索配置
    private boolean enableReretrieval = true;
    private int maxRetrievalDepth = 2;
    private int retrievalDepth = 0;

    // 查询重写配置
    private boolean enableQueryRewrite = true;
    private QueryRewriteConfig queryRewrite = new QueryRewriteConfig();
    private int finalTopK = 10;
    private boolean enableCaching = true;
    private long cacheExpiration = 3600;
    
    // Milvus连接配置
    private MilvusConfig milvus = new MilvusConfig();

    // 混合检索配置
    private HybridSearchConfig hybridSearch = new HybridSearchConfig();

    // 稠密向量检索配置
    private DenseVectorSearchConfig denseVectorSearch = new DenseVectorSearchConfig();

    // 稀疏向量检索配置
    private SparseVectorSearchConfig sparseVectorSearch = new SparseVectorSearchConfig();

    // 重排序配置
    private boolean enableReranking = true;
    private RerankingConfig reranking = new RerankingConfig();

    // 多样性优化配置
    private boolean enableDiversityOptimization = true;
    private DiversityConfig diversity = new DiversityConfig();
    
    @Data
    public static class MilvusConfig {
        private String host = "localhost";
        private int port = 19530;
        private String username = "";
        private String password = "";
        private String database = "default";
        private int connectTimeout = 10000;
        private int keepAliveTime = 30000;
        private boolean secure = false;
    }

    @Data
    public static class HybridSearchConfig {
        private String collectionName = "hybrid_documents";
        private String denseVectorField = "dense_vector";
        private String sparseVectorField = "sparse_vector";
        private int topK = 10;
        private int denseTopK = 50;
        private int sparseTopK = 50;
        private int rrfK = 60;
        private float denseWeight = 0.7f;
        private float sparseWeight = 0.3f;
        private Map<String, Object> denseSearchParams = Map.of("ef", 200);
        private Map<String, Object> sparseSearchParams = Map.of("drop_ratio_search", 0.2);
    }

    @Data
    public static class DenseVectorSearchConfig {
        private int dimension = 1024;
        private String metricType = "COSINE";
        private String indexType = "HNSW";
        private Map<String, Object> indexParams = Map.of(
            "M", 16,
            "efConstruction", 200
        );
        private Map<String, Object> searchParams = Map.of("ef", 200);
    }

    @Data
    public static class SparseVectorSearchConfig {
        private String metricType = "IP";
        private String indexType = "SPARSE_INVERTED_INDEX";
        private Map<String, Object> indexParams = Map.of(
            "drop_ratio_build", 0.2
        );
        private Map<String, Object> searchParams = Map.of(
            "drop_ratio_search", 0.2
        );
        private float minWeight = 0.01f;
    }

    @Data
    public static class HybridCollectionConfig {
        private String denseVectorField = "dense_vector";
        private String sparseVectorField = "sparse_vector";
        private int denseVectorDimension = 1024;
    }

    @Data
    public static class QueryRewriteConfig {
        private boolean enableExpansion = true;
        private boolean enableReformulation = true;
        private boolean enableDecomposition = true;
        private boolean enableMultiQuery = true;
        private double minConfidence = 0.6;
        private int maxRewrittenQueries = 5;

        private QueryExpansionConfig expansionConfig = new QueryExpansionConfig();
        private QueryReformulationConfig reformulationConfig = new QueryReformulationConfig();
        private QueryDecompositionConfig decompositionConfig = new QueryDecompositionConfig();
        private MultiQueryConfig multiQueryConfig = new MultiQueryConfig();
    }

    @Data
    public static class QueryExpansionConfig {
        private int maxSynonyms = 3;
        private int maxRelatedTerms = 2;
        private int maxConceptTerms = 2;
        private String language = "zh";
        private boolean enableSemanticExpansion = true;
        private double synonymWeight = 0.8;
        private double relatedTermWeight = 0.6;
    }

    @Data
    public static class QueryReformulationConfig {
        private String reformulationInstructions = "使查询更清晰、更具体、更易于理解";
        private double minConfidence = 0.7;
        private LLMConfig llmConfig = new LLMConfig();
        private boolean enableGrammarCorrection = true;
        private boolean enableTermNormalization = true;
    }

    @Data
    public static class QueryDecompositionConfig {
        private int maxSubQueries = 3;
        private double minSubQueryConfidence = 0.6;
        private LLMConfig llmConfig = new LLMConfig();
        private boolean enableLogicalDecomposition = true;
        private boolean enableTemporalDecomposition = true;
    }

    @Data
    public static class MultiQueryConfig {
        private int maxPerspectiveQueries = 2;
        private int maxGranularityQueries = 2;
        private int maxProgressiveQueries = 2;
        private double minGenerationConfidence = 0.5;
        private boolean enablePerspectiveGeneration = true;
        private boolean enableGranularityGeneration = true;
        private boolean enableProgressiveGeneration = true;
    }

    @Data
    public static class LLMConfig {
        private String modelName = "gpt-3.5-turbo";
        private double temperature = 0.3;
        private int maxTokens = 200;
        private int timeoutMs = 10000;
    }

    @Data
    public static class CoTConfig {
        private boolean enableIntentAnalysis = true;
        private boolean enableConceptExtraction = true;
        private boolean enableStrategyReasoning = true;
        private boolean enableReasoningPathRecording = true;
        private double minReasoningConfidence = 0.6;

        private IntentAnalysisConfig intentAnalysisConfig = new IntentAnalysisConfig();
        private ConceptExtractionConfig conceptExtractionConfig = new ConceptExtractionConfig();
        private StrategyReasoningConfig strategyReasoningConfig = new StrategyReasoningConfig();
    }

    @Data
    public static class IntentAnalysisConfig {
        private LLMConfig llmConfig = new LLMConfig();
        private boolean enableSemanticAnalysis = true;
        private boolean enableComplexityAssessment = true;
        private List<String> supportedIntents = Arrays.asList(
            "FACTUAL", "ANALYTICAL", "COMPARATIVE", "CREATIVE", "PROCEDURAL"
        );
    }

    @Data
    public static class ConceptExtractionConfig {
        private LLMConfig llmConfig = new LLMConfig();
        private boolean enableNLPIntegration = true;
        private boolean enableKnowledgeGraphIntegration = true;
        private boolean enableConceptHierarchy = true;
        private int maxConcepts = 10;
        private int maxEntities = 15;
    }

    @Data
    public static class StrategyReasoningConfig {
        private LLMConfig llmConfig = new LLMConfig();
        private boolean enableStrategyComparison = true;
        private boolean enableParameterOptimization = true;
        private boolean enableEffectPrediction = true;
        private List<String> availableStrategies = Arrays.asList(
            "VECTOR_SEARCH", "KEYWORD_SEARCH", "HYBRID_SEARCH",
            "QUERY_EXPANSION", "QUERY_REFORMULATION"
        );
    }

    @Data
    public static class ReflectionConfig {
        private boolean enableQualityAssessment = true;
        private boolean enableStrategyAnalysis = true;
        private boolean enableImprovementIdentification = true;
        private boolean enableParameterAdjustment = true;
        private double satisfactionThreshold = 0.7;
        private double qualityThreshold = 0.6;
        private double effectivenessThreshold = 0.6;

        private QualityAssessmentConfig qualityAssessmentConfig = new QualityAssessmentConfig();
        private ImprovementConfig improvementConfig = new ImprovementConfig();
        private ParameterAdjustmentConfig parameterAdjustmentConfig = new ParameterAdjustmentConfig();
    }

    @Data
    public static class QualityAssessmentConfig {
        private LLMConfig llmConfig = new LLMConfig();
        private boolean enableRelevanceAssessment = true;
        private boolean enableCompletenessAssessment = true;
        private boolean enableDiversityAssessment = true;
        private boolean enableLLMAssessment = true;
        private double relevanceWeight = 0.4;
        private double completenessWeight = 0.3;
        private double diversityWeight = 0.2;
        private double llmAssessmentWeight = 0.1;
    }

    @Data
    public static class ImprovementConfig {
        private double qualityThreshold = 0.6;
        private double effectivenessThreshold = 0.6;
        private boolean enableStrategySuggestions = true;
        private boolean enableParameterSuggestions = true;
        private int maxSuggestions = 5;
    }

    @Data
    public static class ParameterAdjustmentConfig {
        private double minConfidence = 0.7;
        private boolean enableIntelligentOptimization = true;
        private Map<String, Object> currentParameters = new HashMap<>();
        private OptimizationConfig optimizationConfig = new OptimizationConfig();
    }

    @Data
    public static class OptimizationConfig {
        private String optimizationAlgorithm = "BAYESIAN";
        private int maxIterations = 10;
        private double convergenceThreshold = 0.01;
        private boolean enableConstraints = true;
    }
}
```

### 数据模型
```java
// CoT推理相关数据模型
@Data
@Builder
public class ReasoningChain {
    private String queryId;
    private String originalQuery;
    private IntentAnalysisResult intentAnalysis;
    private ConceptExtractionResult conceptExtraction;
    private StrategyReasoningResult strategyReasoning;
    private ReasoningPath reasoningPath;
    private long totalReasoningTime;
    private double confidenceScore;
}

@Data
@Builder
public class ReasoningStep {
    private int stepNumber;
    private String stepType;
    private String description;
    private Map<String, Object> input;
    private Map<String, Object> output;
    private String reasoning;
    private double confidence;
    private long processingTime;
}

@Data
@Builder
public class IntentAnalysisResult {
    private String intent;
    private String intentType;
    private double confidence;
    private String reasoning;
    private Map<String, Object> intentFeatures;
    private double complexityScore;
}

@Data
@Builder
public class ConceptExtractionResult {
    private List<Concept> concepts;
    private List<Entity> entities;
    private Map<String, List<String>> conceptHierarchy;
    private double confidence;
    private String reasoning;
    private ConceptGraph conceptGraph;
}

@Data
@Builder
public class StrategyReasoningResult {
    private List<String> recommendedStrategies;
    private Map<String, Object> optimalParameters;
    private Map<String, Double> strategyConfidences;
    private String reasoning;
    private double confidence;
    private List<String> effectPredictions;
}

// 自我反思相关数据模型
@Data
@Builder
public class ReflectionResult {
    private String reflectionId;
    private QualityAssessment qualityAssessment;
    private StrategyEffectivenessAnalysis strategyAnalysis;
    private ImprovementSuggestions improvements;
    private ParameterAdjustment parameterAdjustment;
    private boolean needsReretrieval;
    private double overallSatisfaction;
    private long reflectionTime;
}

@Data
@Builder
public class QualityAssessment {
    private RelevanceAssessment relevanceAssessment;
    private CompletenessAssessment completenessAssessment;
    private DiversityAssessment diversityAssessment;
    private LLMQualityAssessment llmQualityAssessment;
    private double overallQualityScore;
}

@Data
@Builder
public class StrategyEffectivenessAnalysis {
    private CoTEffectivenessAnalysis cotAnalysis;
    private QueryRewriteEffectivenessAnalysis rewriteAnalysis;
    private HybridRetrievalEffectivenessAnalysis hybridAnalysis;
    private double overallEffectiveness;
}

@Data
@Builder
public class ImprovementSuggestions {
    private List<StrategySuggestion> strategySuggestions;
    private List<ParameterSuggestion> parameterSuggestions;
    private String priorityLevel;
}

@Data
@Builder
public class ParameterAdjustment {
    private Map<String, Object> originalParameters;
    private Map<String, Object> adjustedParameters;
    private List<String> adjustmentReasons;
    private double adjustmentConfidence;
}

// 查询重写相关数据模型
@Data
@Builder
public class RewrittenQuery {
    private String originalQuery;
    private String rewrittenQuery;
    private QueryRewriteType type;
    private double confidence;
    private Map<String, Object> metadata;
    private List<String> addedTerms;
    private List<String> removedTerms;
    private long processingTime;
}

public enum QueryRewriteType {
    ORIGINAL,            // 原始查询
    EXPANSION,           // 查询扩展
    REFORMULATION,       // 查询改写
    DECOMPOSITION,       // 查询分解
    MULTI_QUERY,         // 多查询生成
    SIMPLIFICATION,      // 查询简化
    SPECIALIZATION       // 查询专业化
}

@Data
@Builder
public class SearchQuery {
    private String text;
    private String language;
    private Map<String, Object> filters;
    private QueryContext context;
}

@Data
@Builder
public class QueryContext {
    private String userId;
    private String sessionId;
    private List<String> historyQueries;
    private Map<String, Object> userProfile;
    private long timestamp;
}

// 检索结果相关数据模型
@Data
@Builder
public class RetrievalResult {
    private List<RankedResult> results;
    private int totalCandidates;
    private List<String> rewrittenQueries;
    private long processingTime;
    private SearchQuery query;
    private RetrievalStats stats;

    // CoT推理和自我反思相关字段
    private ReasoningChain reasoningChain;
    private ReflectionResult reflectionResult;
    private double qualityScore;
    private double satisfactionScore;
    private boolean wasReRetrieved;
    private int retrievalAttempts;
}

@Data
@Builder
public class RetrievalStats {
    private long queryRewriteTime;
    private long hybridSearchTime;
    private long rerankingTime;
    private long diversityOptimizationTime;
    private long postProcessingTime;
    private int originalQueryResults;
    private int expandedQueryResults;
    private int reformulatedQueryResults;
    private int decomposedQueryResults;
}

@Data
@Builder
public class HybridDocument {
    private String id;
    private String content;
    private float[] denseVector;
    private SparseFloatVector sparseVector;
    private Map<String, Object> metadata;
    private String denseVectorField;
    private String sparseVectorField;
}

@Data
@Builder
public class HybridCandidate {
    private String documentId;
    private String content;
    private float score;
    private int rank;
    private Map<String, Object> metadata;
    private float denseScore;
    private float sparseScore;
    private String source; // "dense", "sparse", "hybrid"
}

@Data
@Builder
public class HybridSearchResult {
    private List<HybridCandidate> candidates;
    private int totalCount;
    private long searchTime;
    private SearchQuery query;
    private HybridSearchStats stats;
}

@Data
@Builder
public class HybridSearchStats {
    private long denseSearchTime;
    private long sparseSearchTime;
    private long fusionTime;
    private int denseResultCount;
    private int sparseResultCount;
    private float avgDenseScore;
    private float avgSparseScore;
}
        private String metricType = "COSINE";
        private Map<String, Object> searchParams = new HashMap<>();
        private boolean enableCaching = true;
    }
    
    @Data
    public static class KeywordSearchConfig {
        private int topK = 50;
        private boolean enableSynonymExpansion = true;
        private boolean enableQueryRewriting = true;
        private boolean enableEntityRecognition = true;
        private double fuzziness = 0.8;
    }
}
```
